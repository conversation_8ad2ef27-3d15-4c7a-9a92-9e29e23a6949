using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IProductRepository : IGenericRepository<Product>
    {
        // Product-specific queries
        Task<Product?> GetByBarcodeAsync(string barcode);
        Task<Product?> GetWithCategoryAsync(int id);
        Task<Product?> GetWithCategoryAndSupplierAsync(int id);
        Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId);
        Task<IEnumerable<Product>> GetBySupplierAsync(int supplierId);
        Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm);
        Task<bool> IsBarcodeUniqueAsync(string barcode, int? excludeId = null);

        // Inventory-related queries
        Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10);
        Task<IEnumerable<Product>> GetOutOfStockProductsAsync();
        Task<Dictionary<int, int>> GetProductsCurrentStockAsync(IEnumerable<int> productIds);
        Task<int> GetProductCurrentStockAsync(int productId);
        Task<IEnumerable<Product>> GetProductsNeedingReorderAsync();

        // Price-related queries
        Task<IEnumerable<Product>> GetProductsByPriceRangeAsync(decimal minPrice, decimal maxPrice);
        Task<IEnumerable<Product>> GetProductsBelowMinPriceAsync();

        // Stock management
        Task<bool> UpdateStockAsync(int productId, int quantity, string reason, int userId);
        Task<bool> AdjustStockAsync(int productId, int newQuantity, string reason, int userId);
        Task<bool> HasSufficientStockAsync(int productId, int requiredQuantity);

        // Analytics and reporting
        Task<int> GetTotalProductCountAsync();
        Task<decimal> GetTotalInventoryValueAsync();
        Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<Product>> GetRecentlyAddedProductsAsync(int count = 10);
        Task<Dictionary<string, object>> GetProductStatisticsAsync();

        // Advanced queries
        Task<PagedResult<Product>> GetProductsWithStockAsync(int pageNumber, int pageSize,
            Expression<Func<Product, bool>>? filter = null);
        Task<IEnumerable<Product>> GetProductsWithLowMarginAsync(decimal marginThreshold = 0.1m);
        Task<IEnumerable<Product>> GetFastMovingProductsAsync(int days = 30);
        Task<IEnumerable<Product>> GetSlowMovingProductsAsync(int days = 90);

        // Validation methods
        Task<bool> HasActiveInventoryAsync(int productId);
        Task<bool> CanDeleteProductAsync(int productId);
        Task<bool> HasPendingOrdersAsync(int productId);

        // Bulk operations
        Task<int> UpdateProductPricesAsync(int categoryId, decimal priceAdjustment, bool isPercentage = false);
        Task<int> UpdateProductPricesAsync(IEnumerable<int> productIds, decimal priceAdjustment, bool isPercentage = false);
        Task<int> BulkUpdateCategoryAsync(IEnumerable<int> productIds, int newCategoryId);

        // Pagination with enhanced features
        Task<(IEnumerable<Product> Products, int TotalCount)> GetPagedProductsAsync(
            int pageNumber, int pageSize, string? searchTerm = null, int? categoryId = null, int? supplierId = null);
    }
}

using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IPurchaseRepository : IGenericRepository<PurchaseInvoice>
    {
        // Purchase-specific queries
        Task<PurchaseInvoice?> GetWithDetailsAsync(int id);
        Task<PurchaseInvoice?> GetWithDetailsAndProductsAsync(int id);
        Task<PurchaseInvoice?> GetByInvoiceNumberAsync(string invoiceNumber);
        Task<IEnumerable<PurchaseInvoice>> GetBySupplierAsync(int supplierId);
        Task<IEnumerable<PurchaseInvoice>> GetByUserAsync(int userId);
        Task<IEnumerable<PurchaseInvoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<PurchaseInvoice>> GetTodaysPurchasesAsync();

        // Purchase analytics
        Task<decimal> GetTotalPurchaseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetTotalPurchaseCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAveragePurchaseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Purchase reports
        Task<IEnumerable<object>> GetDailyPurchaseReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<object>> GetMonthlyPurchaseReportAsync(int year);
        Task<IEnumerable<object>> GetPurchasesBySupplierAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetTopSuppliersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);

        // Supplier performance
        Task<IEnumerable<object>> GetSupplierPerformanceAsync(DateTime fromDate, DateTime toDate);
        Task<decimal> GetSupplierTotalPurchasesAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<PurchaseInvoice>> GetLargePurchasesAsync(decimal minimumAmount);

        // Invoice management
        Task<bool> IsInvoiceNumberUniqueAsync(string invoiceNumber, int? excludeId = null);
        Task<string> GenerateNextInvoiceNumberAsync();
        Task<bool> CanCancelInvoiceAsync(int invoiceId);

        // Returns and quality issues
        Task<IEnumerable<PurchaseReturn>> GetPurchaseReturnsAsync(int purchaseInvoiceId);
        Task<decimal> GetTotalReturnsAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetReturnReasonsReportAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Cost analysis
        Task<IEnumerable<object>> GetCostAnalysisAsync(int productId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAverageCostAsync(int productId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetPriceVariationReportAsync(int productId);

        // Advanced queries
        Task<PagedResult<PurchaseInvoice>> GetPagedPurchasesWithDetailsAsync(int pageNumber, int pageSize, 
            Expression<Func<PurchaseInvoice, bool>>? filter = null);
        Task<IEnumerable<PurchaseInvoice>> GetRecentPurchasesAsync(int count = 10);
        Task<IEnumerable<object>> GetPurchaseTrendsAsync(int days = 30);

        // Pagination with enhanced features
        Task<(IEnumerable<PurchaseInvoice> Purchases, int TotalCount)> GetPagedPurchasesAsync(
            int pageNumber, int pageSize, string? searchTerm = null, DateTime? fromDate = null, DateTime? toDate = null, 
            int? supplierId = null, int? userId = null);

        // Validation
        Task<bool> ValidatePurchaseDetailsAsync(IEnumerable<PurchaseInvoiceDetail> details);
        Task<bool> HasPendingReceiptsAsync(int supplierId);
    }
}

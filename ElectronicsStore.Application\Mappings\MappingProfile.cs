using AutoMapper;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // User mappings
            CreateMap<User, UserDto>()
                .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role.Name));
            CreateMap<CreateUserDto, User>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now));
            CreateMap<UpdateUserDto, User>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore());

            // Role mappings (commented out as RoleDto doesn't exist yet)
            // CreateMap<Role, RoleDto>();

            // Category mappings
            CreateMap<Category, CategoryDto>();
            CreateMap<CreateCategoryDto, Category>();
            CreateMap<UpdateCategoryDto, Category>();

            // Product mappings
            CreateMap<Product, ProductDto>()
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.Category.Name))
                .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : null))
                .ForMember(dest => dest.CurrentQuantity, opt => opt.Ignore()) // Will be set separately
                .ForMember(dest => dest.LastMovementDate, opt => opt.Ignore()); // Will be set separately

            CreateMap<CreateProductDto, Product>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.Category, opt => opt.Ignore())
                .ForMember(dest => dest.Supplier, opt => opt.Ignore());

            CreateMap<UpdateProductDto, Product>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.Category, opt => opt.Ignore())
                .ForMember(dest => dest.Supplier, opt => opt.Ignore());

            // Sales Invoice mappings
            CreateMap<SalesInvoice, SalesInvoiceDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username))
                .ForMember(dest => dest.OverrideByUserName, opt => opt.MapFrom(src => src.OverrideByUser != null ? src.OverrideByUser.Username : null))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.SalesInvoiceDetails));

            CreateMap<CreateSalesInvoiceDto, SalesInvoice>()
                .ForMember(dest => dest.InvoiceDate, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.TotalAmount, opt => opt.Ignore()) // Will be calculated
                .ForMember(dest => dest.SalesInvoiceDetails, opt => opt.Ignore()) // Will be handled separately
                .ForMember(dest => dest.User, opt => opt.Ignore())
                .ForMember(dest => dest.OverrideByUser, opt => opt.Ignore());

            // Sales Invoice Detail mappings
            CreateMap<SalesInvoiceDetail, SalesInvoiceDetailDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
                .ForMember(dest => dest.LineTotal, opt => opt.MapFrom(src => (src.UnitPrice - src.DiscountAmount) * src.Quantity));

            CreateMap<CreateSalesInvoiceDetailDto, SalesInvoiceDetail>()
                .ForMember(dest => dest.Product, opt => opt.Ignore())
                .ForMember(dest => dest.SalesInvoice, opt => opt.Ignore());

            // Purchase Invoice mappings
            CreateMap<PurchaseInvoice, PurchaseInvoiceDto>()
                .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : null))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.PurchaseInvoiceDetails));

            CreateMap<CreatePurchaseInvoiceDto, PurchaseInvoice>()
                .ForMember(dest => dest.InvoiceDate, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.TotalAmount, opt => opt.Ignore()) // Will be calculated
                .ForMember(dest => dest.PurchaseInvoiceDetails, opt => opt.Ignore()) // Will be handled separately
                .ForMember(dest => dest.Supplier, opt => opt.Ignore())
                .ForMember(dest => dest.User, opt => opt.Ignore());

            // Purchase Invoice Detail mappings
            CreateMap<PurchaseInvoiceDetail, PurchaseInvoiceDetailDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
                .ForMember(dest => dest.LineTotal, opt => opt.MapFrom(src => src.UnitCost * src.Quantity));

            CreateMap<CreatePurchaseInvoiceDetailDto, PurchaseInvoiceDetail>()
                .ForMember(dest => dest.Product, opt => opt.Ignore())
                .ForMember(dest => dest.PurchaseInvoice, opt => opt.Ignore());

            // Inventory Log mappings
            CreateMap<InventoryLog, InventoryLogDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username));

            CreateMap<CreateInventoryLogDto, InventoryLog>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.Product, opt => opt.Ignore())
                .ForMember(dest => dest.User, opt => opt.Ignore());

            // Supplier mappings
            CreateMap<Supplier, SupplierDto>();
            CreateMap<CreateSupplierDto, Supplier>();
            CreateMap<UpdateSupplierDto, Supplier>();

            // Expense mappings
            CreateMap<Expense, ExpenseDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username));

            CreateMap<CreateExpenseDto, Expense>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.User, opt => opt.Ignore());

            CreateMap<UpdateExpenseDto, Expense>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.User, opt => opt.Ignore());

            // Sales Return mappings
            CreateMap<SalesReturn, SalesReturnDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username))
                .ForMember(dest => dest.InvoiceNumber, opt => opt.MapFrom(src => src.SalesInvoice.InvoiceNumber));

            // Purchase Return mappings
            CreateMap<PurchaseReturn, PurchaseReturnDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product.Name))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User.Username))
                .ForMember(dest => dest.InvoiceNumber, opt => opt.MapFrom(src => src.PurchaseInvoice.InvoiceNumber));

            // Report DTOs (these are typically created from queries, not entities)
            CreateMap<object, SalesReportDto>(); // Dynamic mapping for reports
            CreateMap<object, TopCustomerDto>();
            CreateMap<object, TopProductDto>();
            CreateMap<object, InventoryValuationDto>();
            CreateMap<object, LowStockAlertDto>();
            CreateMap<object, InventoryMovementDto>();
            CreateMap<object, DailyMovementDto>();
            CreateMap<object, ProductStockDto>();
        }
    }
}

{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=ElectronicsStoreDB;Trusted_Connection=true;TrustServerCertificate=true;"}, "JwtSettings": {"SecretKey": "ElectronicsStore_SuperSecretKey_2024_ForJWT_Authentication_MinimumLength32Characters", "Issuer": "ElectronicsStoreAPI", "Audience": "ElectronicsStoreUsers", "ExpirationInMinutes": 60, "RefreshTokenExpirationInDays": 7}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/electronics-store-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "shared": true, "flushToDiskInterval": "00:00:01", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*"}
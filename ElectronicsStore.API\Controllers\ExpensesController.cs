using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ExpensesController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public ExpensesController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get all expenses
        /// </summary>
        /// <returns>List of expenses</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpenses()
        {
            var expenses = await _unitOfWork.Expenses.GetAllAsync();
            var expenseDtos = expenses.Select(e => new ExpenseDto
            {
                Id = e.Id,
                Description = e.Description,
                Amount = e.Amount,
                Category = e.Category,
                ExpenseDate = e.ExpenseDate,
                UserId = e.UserId,
                UserName = e.User?.Username ?? "",
                Notes = e.Notes,
                ReceiptNumber = e.ReceiptNumber,
                CreatedAt = e.CreatedAt
            });

            return Ok(expenseDtos);
        }

        /// <summary>
        /// Get expense by ID
        /// </summary>
        /// <param name="id">Expense ID</param>
        /// <returns>Expense details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> GetExpense(int id)
        {
            var expense = await _unitOfWork.Expenses.GetByIdAsync(id);
            if (expense == null)
                return NotFound($"Expense with ID {id} not found.");

            var expenseDto = new ExpenseDto
            {
                Id = expense.Id,
                Description = expense.Description,
                Amount = expense.Amount,
                Category = expense.Category,
                ExpenseDate = expense.ExpenseDate,
                UserId = expense.UserId,
                UserName = expense.User?.Username ?? "",
                Notes = expense.Notes,
                ReceiptNumber = expense.ReceiptNumber,
                CreatedAt = expense.CreatedAt
            };

            return Ok(expenseDto);
        }

        /// <summary>
        /// Create a new expense
        /// </summary>
        /// <param name="createExpenseDto">Expense creation data</param>
        /// <returns>Created expense</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> CreateExpense(CreateExpenseDto createExpenseDto)
        {
            try
            {
                // Validation
                if (!await _unitOfWork.Users.AnyAsync(u => u.Id == createExpenseDto.UserId))
                    return BadRequest($"User with ID {createExpenseDto.UserId} not found.");

                if (createExpenseDto.Amount <= 0)
                    return BadRequest("Expense amount must be greater than zero.");

                var validCategories = new[] { "rent", "utilities", "supplies", "marketing", "maintenance", "salaries", "other" };
                if (!validCategories.Contains(createExpenseDto.Category.ToLower()))
                    return BadRequest("Invalid expense category.");

                var expense = new Domain.Entities.Expense
                {
                    Description = createExpenseDto.Description,
                    Amount = createExpenseDto.Amount,
                    Category = createExpenseDto.Category.ToLower(),
                    ExpenseDate = createExpenseDto.ExpenseDate,
                    UserId = createExpenseDto.UserId,
                    Notes = createExpenseDto.Notes,
                    ReceiptNumber = createExpenseDto.ReceiptNumber,
                    CreatedAt = DateTime.Now
                };

                await _unitOfWork.Expenses.AddAsync(expense);
                await _unitOfWork.SaveChangesAsync();

                return CreatedAtAction(nameof(GetExpense), new { id = expense.Id }, 
                    await GetExpense(expense.Id));
            }
            catch (Exception ex)
            {
                return BadRequest($"Error creating expense: {ex.Message}");
            }
        }

        /// <summary>
        /// Update an existing expense
        /// </summary>
        /// <param name="id">Expense ID</param>
        /// <param name="updateExpenseDto">Expense update data</param>
        /// <returns>Updated expense</returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> UpdateExpense(int id, UpdateExpenseDto updateExpenseDto)
        {
            if (id != updateExpenseDto.Id)
                return BadRequest("ID mismatch.");

            try
            {
                var expense = await _unitOfWork.Expenses.GetByIdAsync(id);
                if (expense == null)
                    return NotFound($"Expense with ID {id} not found.");

                if (updateExpenseDto.Amount <= 0)
                    return BadRequest("Expense amount must be greater than zero.");

                var validCategories = new[] { "rent", "utilities", "supplies", "marketing", "maintenance", "salaries", "other" };
                if (!validCategories.Contains(updateExpenseDto.Category.ToLower()))
                    return BadRequest("Invalid expense category.");

                expense.Description = updateExpenseDto.Description;
                expense.Amount = updateExpenseDto.Amount;
                expense.Category = updateExpenseDto.Category.ToLower();
                expense.ExpenseDate = updateExpenseDto.ExpenseDate;
                expense.Notes = updateExpenseDto.Notes;
                expense.ReceiptNumber = updateExpenseDto.ReceiptNumber;

                _unitOfWork.Expenses.Update(expense);
                await _unitOfWork.SaveChangesAsync();

                return Ok(await GetExpense(id));
            }
            catch (Exception ex)
            {
                return BadRequest($"Error updating expense: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete an expense
        /// </summary>
        /// <param name="id">Expense ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeleteExpense(int id)
        {
            try
            {
                var expense = await _unitOfWork.Expenses.GetByIdAsync(id);
                if (expense == null)
                    return NotFound($"Expense with ID {id} not found.");

                _unitOfWork.Expenses.Remove(expense);
                await _unitOfWork.SaveChangesAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest($"Error deleting expense: {ex.Message}");
            }
        }

        /// <summary>
        /// Search expenses
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching expenses</returns>
        [HttpPost("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> SearchExpenses(ExpenseSearchDto searchDto)
        {
            var expenses = await _unitOfWork.Expenses.GetAllAsync();
            var filteredExpenses = expenses.AsQueryable();

            if (!string.IsNullOrEmpty(searchDto.SearchTerm))
            {
                filteredExpenses = filteredExpenses.Where(e => 
                    e.Description.Contains(searchDto.SearchTerm) ||
                    (e.Notes != null && e.Notes.Contains(searchDto.SearchTerm)) ||
                    (e.ReceiptNumber != null && e.ReceiptNumber.Contains(searchDto.SearchTerm)));
            }

            if (!string.IsNullOrEmpty(searchDto.Category))
                filteredExpenses = filteredExpenses.Where(e => e.Category == searchDto.Category.ToLower());

            if (searchDto.UserId.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.UserId == searchDto.UserId.Value);

            if (searchDto.FromDate.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.ExpenseDate >= searchDto.FromDate.Value);

            if (searchDto.ToDate.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.ExpenseDate <= searchDto.ToDate.Value);

            if (searchDto.MinAmount.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.Amount >= searchDto.MinAmount.Value);

            if (searchDto.MaxAmount.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.Amount <= searchDto.MaxAmount.Value);

            var expenseDtos = filteredExpenses.Select(e => new ExpenseDto
            {
                Id = e.Id,
                Description = e.Description,
                Amount = e.Amount,
                Category = e.Category,
                ExpenseDate = e.ExpenseDate,
                UserId = e.UserId,
                UserName = e.User?.Username ?? "",
                Notes = e.Notes,
                ReceiptNumber = e.ReceiptNumber,
                CreatedAt = e.CreatedAt
            });

            return Ok(expenseDtos);
        }

        /// <summary>
        /// Get expenses by category
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Expenses grouped by category</returns>
        [HttpGet("by-category")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseCategoryDto>>> GetExpensesByCategory(
            [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var expenses = await _unitOfWork.Expenses.GetAllAsync();
            var filteredExpenses = expenses.AsQueryable();

            if (fromDate.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.ExpenseDate >= fromDate.Value);

            if (toDate.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.ExpenseDate <= toDate.Value);

            var totalAmount = filteredExpenses.Sum(e => e.Amount);

            var categoryBreakdown = filteredExpenses
                .GroupBy(e => e.Category)
                .Select(g => new ExpenseCategoryDto
                {
                    Category = g.Key,
                    Count = g.Count(),
                    TotalAmount = g.Sum(e => e.Amount),
                    AverageAmount = g.Average(e => e.Amount),
                    Percentage = totalAmount > 0 ? (g.Sum(e => e.Amount) / totalAmount) * 100 : 0
                })
                .OrderByDescending(c => c.TotalAmount);

            return Ok(categoryBreakdown);
        }

        /// <summary>
        /// Get monthly expenses report
        /// </summary>
        /// <param name="year">Year</param>
        /// <returns>Monthly expenses breakdown</returns>
        [HttpGet("monthly/{year}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<MonthlyExpenseDto>>> GetMonthlyExpenses(int year)
        {
            var expenses = await _unitOfWork.Expenses.GetAllAsync();
            var yearExpenses = expenses.Where(e => e.ExpenseDate.Year == year);

            var monthlyExpenses = yearExpenses
                .GroupBy(e => e.ExpenseDate.Month)
                .Select(g => new MonthlyExpenseDto
                {
                    Year = year,
                    Month = g.Key,
                    MonthName = new DateTime(year, g.Key, 1).ToString("MMMM"),
                    TotalExpenses = g.Count(),
                    TotalAmount = g.Sum(e => e.Amount),
                    AverageAmount = g.Average(e => e.Amount)
                })
                .OrderBy(m => m.Month);

            return Ok(monthlyExpenses);
        }

        /// <summary>
        /// Get expense statistics
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Expense statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetExpenseStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var expenses = await _unitOfWork.Expenses.GetAllAsync();
            var filteredExpenses = expenses.AsQueryable();

            if (fromDate.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.ExpenseDate >= fromDate.Value);

            if (toDate.HasValue)
                filteredExpenses = filteredExpenses.Where(e => e.ExpenseDate <= toDate.Value);

            var statistics = new
            {
                TotalExpenses = filteredExpenses.Count(),
                TotalAmount = filteredExpenses.Sum(e => e.Amount),
                AverageAmount = filteredExpenses.Any() ? filteredExpenses.Average(e => e.Amount) : 0,
                Categories = filteredExpenses.GroupBy(e => e.Category).Count(),
                TopCategory = filteredExpenses.GroupBy(e => e.Category)
                    .OrderByDescending(g => g.Sum(e => e.Amount))
                    .FirstOrDefault()?.Key ?? "N/A"
            };

            return Ok(statistics);
        }
    }
}

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using AutoMapper;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ExpensesController : ControllerBase
    {
        private readonly IExpenseService _expenseService;
        private readonly IMapper _mapper;

        public ExpensesController(IExpenseService expenseService, IMapper mapper)
        {
            _expenseService = expenseService;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all expenses
        /// </summary>
        /// <returns>List of expenses</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpenses()
        {
            var expenses = await _expenseService.GetAllExpensesAsync();
            return Ok(expenses);
        }

        /// <summary>
        /// Get expense by ID
        /// </summary>
        /// <param name="id">Expense ID</param>
        /// <returns>Expense details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> GetExpense(int id)
        {
            var expense = await _expenseService.GetExpenseByIdAsync(id);
            if (expense == null)
                return NotFound($"Expense with ID {id} not found.");

            return Ok(expense);
        }

        /// <summary>
        /// Create a new expense
        /// </summary>
        /// <param name="createExpenseDto">Expense creation data</param>
        /// <returns>Created expense</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> CreateExpense(CreateExpenseDto createExpenseDto)
        {
            try
            {
                var expense = await _expenseService.CreateExpenseAsync(createExpenseDto);
                return CreatedAtAction(nameof(GetExpense), new { id = expense.Id }, expense);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Update an existing expense
        /// </summary>
        /// <param name="id">Expense ID</param>
        /// <param name="updateExpenseDto">Expense update data</param>
        /// <returns>Updated expense</returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ExpenseDto>> UpdateExpense(int id, UpdateExpenseDto updateExpenseDto)
        {
            if (id != updateExpenseDto.Id)
                return BadRequest("ID mismatch.");

            try
            {
                var expense = await _expenseService.UpdateExpenseAsync(updateExpenseDto);
                return Ok(expense);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Delete an expense
        /// </summary>
        /// <param name="id">Expense ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeleteExpense(int id)
        {
            try
            {
                var result = await _expenseService.DeleteExpenseAsync(id);
                if (!result)
                    return NotFound($"Expense with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Search expenses
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching expenses</returns>
        [HttpPost("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> SearchExpenses(ExpenseSearchDto searchDto)
        {
            var expenses = await _expenseService.SearchExpensesAsync(searchDto);
            return Ok(expenses);
        }

        /// <summary>
        /// Get expenses by category
        /// </summary>
        /// <param name="category">Category name</param>
        /// <returns>List of expenses in category</returns>
        [HttpGet("category/{category}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpensesByCategory(string category)
        {
            var expenses = await _expenseService.GetExpensesByCategoryAsync(category);
            return Ok(expenses);
        }

        /// <summary>
        /// Get expenses by user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of user's expenses</returns>
        [HttpGet("user/{userId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpensesByUser(int userId)
        {
            var expenses = await _expenseService.GetExpensesByUserAsync(userId);
            return Ok(expenses);
        }

        /// <summary>
        /// Get today's expenses
        /// </summary>
        /// <returns>List of today's expenses</returns>
        [HttpGet("today")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetTodaysExpenses()
        {
            var expenses = await _expenseService.GetTodaysExpensesAsync();
            return Ok(expenses);
        }

        /// <summary>
        /// Get expense statistics
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Expense statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetExpenseStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var statistics = await _expenseService.GetExpenseStatisticsAsync(fromDate, toDate);
            return Ok(statistics);
        }

        /// <summary>
        /// Get daily expense report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Daily expense report</returns>
        [HttpGet("reports/daily")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseReportDto>>> GetDailyExpenseReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _expenseService.GetDailyExpenseReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get monthly expense report
        /// </summary>
        /// <param name="year">Year</param>
        /// <returns>Monthly expense report</returns>
        [HttpGet("reports/monthly/{year}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseReportDto>>> GetMonthlyExpenseReport(int year)
        {
            var report = await _expenseService.GetMonthlyExpenseReportAsync(year);
            return Ok(report);
        }

        /// <summary>
        /// Get expenses by category report
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Expenses breakdown by category</returns>
        [HttpGet("reports/categories")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseCategoryDto>>> GetExpensesByCategoryReport([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var report = await _expenseService.GetExpensesByCategoryReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get expense categories
        /// </summary>
        /// <returns>List of expense categories</returns>
        [HttpGet("categories")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<string>>> GetExpenseCategories()
        {
            var categories = await _expenseService.GetExpenseCategoriesAsync();
            return Ok(categories);
        }

        /// <summary>
        /// Get expenses by month
        /// </summary>
        /// <param name="year">Year</param>
        /// <param name="month">Month</param>
        /// <returns>List of expenses for the specified month</returns>
        [HttpGet("month/{year}/{month}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseDto>>> GetExpensesByMonth(int year, int month)
        {
            var expenses = await _expenseService.GetExpensesByMonthAsync(year, month);
            return Ok(expenses);
        }

        /// <summary>
        /// Get paged expenses
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of expenses</returns>
        [HttpGet("paged")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPagedExpenses(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] ExpenseSearchDto? searchDto = null)
        {
            var (expenses, totalCount) = await _expenseService.GetPagedExpensesAsync(pageNumber, pageSize, searchDto);
            
            return Ok(new
            {
                Expenses = expenses,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
    }
}

using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Application.Interfaces
{
    public interface ISalesRepository : IGenericRepository<SalesInvoice>
    {
        // Sales-specific queries
        Task<SalesInvoice?> GetWithDetailsAsync(int id);
        Task<SalesInvoice?> GetWithDetailsAndProductsAsync(int id);
        Task<SalesInvoice?> GetByInvoiceNumberAsync(string invoiceNumber);
        Task<IEnumerable<SalesInvoice>> GetByUserAsync(int userId);
        Task<IEnumerable<SalesInvoice>> GetByCustomerAsync(string customerName);
        Task<IEnumerable<SalesInvoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SalesInvoice>> GetTodaysSalesAsync();
        Task<IEnumerable<SalesInvoice>> GetByPaymentMethodAsync(string paymentMethod);

        // Sales analytics
        Task<decimal> GetTotalSalesAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetTotalSalesCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAverageSaleAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetTotalDiscountAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetTotalProfitAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Sales reports
        Task<IEnumerable<object>> GetDailySalesReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<object>> GetMonthlySalesReportAsync(int year);
        Task<IEnumerable<object>> GetYearlySalesReportAsync();
        Task<IEnumerable<object>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetSalesByPaymentMethodAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetSalesByUserAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Advanced analytics
        Task<IEnumerable<object>> GetHourlySalesAsync(DateTime date);
        Task<IEnumerable<object>> GetSalesTrendsAsync(int days = 30);
        Task<Dictionary<string, decimal>> GetSalesKPIsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<SalesInvoice>> GetLargeSalesAsync(decimal minimumAmount);
        Task<IEnumerable<SalesInvoice>> GetSalesWithDiscountsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Invoice management
        Task<bool> IsInvoiceNumberUniqueAsync(string invoiceNumber, int? excludeId = null);
        Task<string> GenerateNextInvoiceNumberAsync();
        Task<IEnumerable<SalesInvoice>> GetOverriddenSalesAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<bool> CanCancelInvoiceAsync(int invoiceId);

        // Returns and refunds
        Task<IEnumerable<SalesReturn>> GetSalesReturnsAsync(int salesInvoiceId);
        Task<decimal> GetTotalReturnsAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetReturnReasonsReportAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Pagination with enhanced features
        Task<PagedResult<SalesInvoice>> GetPagedSalesWithDetailsAsync(int pageNumber, int pageSize,
            Expression<Func<SalesInvoice, bool>>? filter = null);
        Task<(IEnumerable<SalesInvoice> Sales, int TotalCount)> GetPagedSalesAsync(
            int pageNumber, int pageSize, string? searchTerm = null, DateTime? fromDate = null, DateTime? toDate = null,
            string? paymentMethod = null, int? userId = null);

        // Validation
        Task<bool> HasValidStockForSaleAsync(IEnumerable<SalesInvoiceDetail> details);
        Task<bool> ValidateMinimumPricesAsync(IEnumerable<SalesInvoiceDetail> details, int? overrideUserId = null);
    }
}

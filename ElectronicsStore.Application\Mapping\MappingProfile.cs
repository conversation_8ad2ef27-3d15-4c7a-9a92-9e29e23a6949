using AutoMapper;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // User mappings
            CreateMap<User, UserDto>()
                .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.Role != null ? src.Role.Name : ""));
            CreateMap<CreateUserDto, User>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now));
            CreateMap<UpdateUserDto, User>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore());

            // Role mappings
            CreateMap<Role, RoleDto>();

            // Category mappings
            CreateMap<Category, CategoryDto>();
            CreateMap<CreateCategoryDto, Category>();
            CreateMap<UpdateCategoryDto, Category>();

            // Product mappings
            CreateMap<Product, ProductDto>()
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => src.Category != null ? src.Category.Name : ""))
                .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : ""));
            CreateMap<CreateProductDto, Product>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now));
            CreateMap<UpdateProductDto, Product>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore());

            // Supplier mappings
            CreateMap<Supplier, SupplierDto>();
            CreateMap<CreateSupplierDto, Supplier>();
            CreateMap<UpdateSupplierDto, Supplier>();

            // Sales mappings
            CreateMap<SalesInvoice, SalesInvoiceDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ? src.User.Username : ""))
                .ForMember(dest => dest.OverrideByUserName, opt => opt.MapFrom(src => src.OverrideByUser != null ? src.OverrideByUser.Username : ""))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.SalesInvoiceDetails));

            CreateMap<SalesInvoiceDetail, SalesInvoiceDetailDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : ""));

            CreateMap<CreateSalesInvoiceDto, SalesInvoice>()
                .ForMember(dest => dest.InvoiceDate, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.Details, opt => opt.Ignore());

            CreateMap<CreateSalesInvoiceDetailDto, SalesInvoiceDetail>();

            // Purchase mappings
            CreateMap<PurchaseInvoice, PurchaseInvoiceDto>()
                .ForMember(dest => dest.SupplierName, opt => opt.MapFrom(src => src.Supplier != null ? src.Supplier.Name : ""))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ? src.User.Username : ""))
                .ForMember(dest => dest.Details, opt => opt.MapFrom(src => src.PurchaseInvoiceDetails));

            CreateMap<PurchaseInvoiceDetail, PurchaseInvoiceDetailDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : ""));

            CreateMap<CreatePurchaseInvoiceDto, PurchaseInvoice>()
                .ForMember(dest => dest.InvoiceDate, opt => opt.MapFrom(src => DateTime.Now))
                .ForMember(dest => dest.Details, opt => opt.Ignore());

            CreateMap<CreatePurchaseInvoiceDetailDto, PurchaseInvoiceDetail>();

            // Expense mappings
            CreateMap<Expense, ExpenseDto>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ? src.User.Username : ""));
            CreateMap<CreateExpenseDto, Expense>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now));
            CreateMap<UpdateExpenseDto, Expense>()
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UserId, opt => opt.Ignore());

            // Inventory mappings
            CreateMap<InventoryLog, InventoryLogDto>()
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : ""))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ? src.User.Username : ""));
            CreateMap<CreateInventoryLogDto, InventoryLog>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now));

            // Sales Return mappings
            CreateMap<SalesReturn, SalesReturnDto>()
                .ForMember(dest => dest.InvoiceNumber, opt => opt.MapFrom(src => src.SalesInvoice != null ? src.SalesInvoice.InvoiceNumber : ""))
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : ""))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ? src.User.Username : ""));
            CreateMap<CreateSalesReturnDto, SalesReturn>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now));

            // Purchase Return mappings
            CreateMap<PurchaseReturn, PurchaseReturnDto>()
                .ForMember(dest => dest.InvoiceNumber, opt => opt.MapFrom(src => src.PurchaseInvoice != null ? src.PurchaseInvoice.InvoiceNumber : ""))
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Product != null ? src.Product.Name : ""))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.User != null ? src.User.Username : ""));
            CreateMap<CreatePurchaseReturnDto, PurchaseReturn>()
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.Now));
        }
    }
}

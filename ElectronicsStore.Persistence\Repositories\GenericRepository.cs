using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using System.Linq.Expressions;

namespace ElectronicsStore.Persistence.Repositories
{
    public class GenericRepository<T> : IGenericRepository<T> where T : class
    {
        protected readonly ElectronicsDbContext _context;
        protected readonly DbSet<T> _dbSet;

        public GenericRepository(ElectronicsDbContext context)
        {
            _context = context;
            _dbSet = context.Set<T>();
        }

        #region Basic CRUD Operations

        public virtual async Task<T?> GetByIdAsync(int id)
        {
            return await _dbSet.FindAsync(id);
        }

        public virtual async Task<T?> GetByIdAsync(object id)
        {
            return await _dbSet.FindAsync(id);
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            return await _dbSet.ToListAsync();
        }

        public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> expression)
        {
            return await _dbSet.Where(expression).ToListAsync();
        }

        public virtual async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> expression)
        {
            return await _dbSet.FirstOrDefaultAsync(expression);
        }

        public virtual async Task<T?> LastOrDefaultAsync(Expression<Func<T, bool>> expression)
        {
            return await _dbSet.Where(expression).OrderBy(e => EF.Property<object>(e, "Id")).LastOrDefaultAsync();
        }

        public virtual async Task<bool> AnyAsync(Expression<Func<T, bool>> expression)
        {
            return await _dbSet.AnyAsync(expression);
        }

        public virtual async Task<int> CountAsync(Expression<Func<T, bool>>? expression = null)
        {
            if (expression == null)
                return await _dbSet.CountAsync();

            return await _dbSet.CountAsync(expression);
        }

        #endregion

        #region Add Operations

        public virtual async Task AddAsync(T entity)
        {
            await _dbSet.AddAsync(entity);
        }

        public virtual async Task AddRangeAsync(IEnumerable<T> entities)
        {
            await _dbSet.AddRangeAsync(entities);
        }

        #endregion

        #region Update Operations

        public virtual void Update(T entity)
        {
            _dbSet.Update(entity);
        }

        public virtual void UpdateRange(IEnumerable<T> entities)
        {
            _dbSet.UpdateRange(entities);
        }

        public virtual async Task<T> UpdateAsync(T entity)
        {
            _dbSet.Update(entity);
            await _context.SaveChangesAsync();
            return entity;
        }

        #endregion

        #region Delete Operations

        public virtual void Remove(T entity)
        {
            _dbSet.Remove(entity);
        }

        public virtual void RemoveRange(IEnumerable<T> entities)
        {
            _dbSet.RemoveRange(entities);
        }

        public virtual async Task<bool> RemoveByIdAsync(int id)
        {
            var entity = await GetByIdAsync(id);
            if (entity == null)
                return false;

            Remove(entity);
            return true;
        }

        public virtual async Task<bool> RemoveByIdAsync(object id)
        {
            var entity = await GetByIdAsync(id);
            if (entity == null)
                return false;

            Remove(entity);
            return true;
        }

        #endregion

        #region Pagination

        public virtual async Task<PagedResult<T>> GetPagedResultAsync(int pageNumber, int pageSize,
            Expression<Func<T, bool>>? filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            params Expression<Func<T, object>>[] includes)
        {
            IQueryable<T> query = _dbSet;

            // Apply includes
            foreach (var include in includes)
            {
                query = query.Include(include);
            }

            // Apply filter
            if (filter != null)
                query = query.Where(filter);

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply ordering
            if (orderBy != null)
                query = orderBy(query);

            // Apply pagination
            var data = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<T>
            {
                Data = data,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public virtual async Task<IEnumerable<T>> GetPagedAsync(int pageNumber, int pageSize,
            Expression<Func<T, bool>>? filter = null)
        {
            IQueryable<T> query = _dbSet;

            if (filter != null)
                query = query.Where(filter);

            return await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        #endregion

        #region Include Operations

        public virtual async Task<IEnumerable<T>> GetWithIncludeAsync(params Expression<Func<T, object>>[] includes)
        {
            IQueryable<T> query = _dbSet;

            foreach (var include in includes)
            {
                query = query.Include(include);
            }

            return await query.ToListAsync();
        }

        public virtual async Task<T?> GetByIdWithIncludeAsync(int id, params Expression<Func<T, object>>[] includes)
        {
            IQueryable<T> query = _dbSet;

            foreach (var include in includes)
            {
                query = query.Include(include);
            }

            // Assuming T has an Id property
            var parameter = Expression.Parameter(typeof(T), "x");
            var property = Expression.Property(parameter, "Id");
            var constant = Expression.Constant(id);
            var equal = Expression.Equal(property, constant);
            var lambda = Expression.Lambda<Func<T, bool>>(equal, parameter);

            return await query.FirstOrDefaultAsync(lambda);
        }

        public virtual async Task<IEnumerable<T>> FindWithIncludeAsync(Expression<Func<T, bool>> expression,
            params Expression<Func<T, object>>[] includes)
        {
            IQueryable<T> query = _dbSet;

            foreach (var include in includes)
            {
                query = query.Include(include);
            }

            return await query.Where(expression).ToListAsync();
        }

        #endregion

        #region Ordering and Sorting

        public virtual async Task<IEnumerable<T>> GetOrderedAsync<TKey>(Expression<Func<T, TKey>> orderBy, bool ascending = true)
        {
            IQueryable<T> query = _dbSet;

            if (ascending)
                query = query.OrderBy(orderBy);
            else
                query = query.OrderByDescending(orderBy);

            return await query.ToListAsync();
        }

        public virtual async Task<IEnumerable<T>> GetOrderedAsync<TKey>(Expression<Func<T, bool>> filter,
            Expression<Func<T, TKey>> orderBy, bool ascending = true)
        {
            IQueryable<T> query = _dbSet.Where(filter);

            if (ascending)
                query = query.OrderBy(orderBy);
            else
                query = query.OrderByDescending(orderBy);

            return await query.ToListAsync();
        }

        #endregion

        #region Advanced Queries

        public virtual async Task<IEnumerable<TResult>> SelectAsync<TResult>(Expression<Func<T, TResult>> selector)
        {
            return await _dbSet.Select(selector).ToListAsync();
        }

        public virtual async Task<IEnumerable<TResult>> SelectAsync<TResult>(Expression<Func<T, bool>> filter,
            Expression<Func<T, TResult>> selector)
        {
            return await _dbSet.Where(filter).Select(selector).ToListAsync();
        }

        #endregion

        #region Aggregation

        public virtual async Task<TResult> MaxAsync<TResult>(Expression<Func<T, TResult>> selector)
        {
            return await _dbSet.MaxAsync(selector);
        }

        public virtual async Task<TResult> MinAsync<TResult>(Expression<Func<T, TResult>> selector)
        {
            return await _dbSet.MinAsync(selector);
        }

        public virtual async Task<decimal> SumAsync(Expression<Func<T, decimal>> selector)
        {
            return await _dbSet.SumAsync(selector);
        }

        public virtual async Task<double> AverageAsync(Expression<Func<T, int>> selector)
        {
            return await _dbSet.AverageAsync(selector);
        }

        public virtual async Task<double> AverageAsync(Expression<Func<T, decimal>> selector)
        {
            return (double)await _dbSet.AverageAsync(selector);
        }

        #endregion

        #region Bulk Operations

        public virtual async Task<int> BulkUpdateAsync(Expression<Func<T, bool>> filter, Expression<Func<T, T>> updateExpression)
        {
            var entities = await _dbSet.Where(filter).ToListAsync();
            var compiled = updateExpression.Compile();

            foreach (var entity in entities)
            {
                var updated = compiled(entity);
                _context.Entry(entity).CurrentValues.SetValues(updated);
            }

            return await _context.SaveChangesAsync();
        }

        public virtual async Task<int> BulkDeleteAsync(Expression<Func<T, bool>> filter)
        {
            var entities = await _dbSet.Where(filter).ToListAsync();
            _dbSet.RemoveRange(entities);
            return await _context.SaveChangesAsync();
        }

        #endregion

        #region Existence Checks

        public virtual async Task<bool> ExistsAsync(int id)
        {
            return await _dbSet.FindAsync(id) != null;
        }

        public virtual async Task<bool> ExistsAsync(object id)
        {
            return await _dbSet.FindAsync(id) != null;
        }

        public virtual async Task<bool> ExistsAsync(Expression<Func<T, bool>> expression)
        {
            return await _dbSet.AnyAsync(expression);
        }

        #endregion
    }
}

using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IPurchaseService
    {
        // Basic CRUD operations
        Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByIdAsync(int id);
        Task<PurchaseInvoiceDto?> GetPurchaseInvoiceByNumberAsync(string invoiceNumber);
        Task<IEnumerable<PurchaseInvoiceDto>> GetAllPurchaseInvoicesAsync();
        Task<PurchaseInvoiceDto> CreatePurchaseInvoiceAsync(CreatePurchaseInvoiceDto createPurchaseInvoiceDto);
        Task<bool> DeletePurchaseInvoiceAsync(int id);

        // Search and filtering
        Task<IEnumerable<PurchaseInvoiceDto>> SearchPurchaseInvoicesAsync(PurchaseSearchDto searchDto);
        Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesBySupplierAsync(int supplierId);
        Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesByUserAsync(int userId);
        Task<IEnumerable<PurchaseInvoiceDto>> GetPurchaseInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<PurchaseInvoiceDto>> GetTodaysPurchasesAsync();

        // Purchase analytics
        Task<decimal> GetTotalPurchaseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetTotalPurchaseCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAveragePurchaseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Reports
        Task<IEnumerable<object>> GetDailyPurchaseReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<object>> GetMonthlyPurchaseReportAsync(int year);
        Task<IEnumerable<object>> GetPurchasesBySupplierAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Top suppliers and products
        Task<IEnumerable<object>> GetTopSuppliersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<object>> GetTopPurchasedProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);

        // Returns management
        Task<bool> ProcessPurchaseReturnAsync(int purchaseInvoiceId, int productId, int quantity, string reason, int userId);
        Task<IEnumerable<PurchaseReturnDto>> GetPurchaseReturnsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Validation
        Task<bool> PurchaseInvoiceExistsAsync(int id);
        Task<bool> InvoiceNumberExistsAsync(string invoiceNumber);
        Task<bool> CanDeletePurchaseInvoiceAsync(int id);

        // Pagination
        Task<(IEnumerable<PurchaseInvoiceDto> Purchases, int TotalCount)> GetPagedPurchaseInvoicesAsync(
            int pageNumber, int pageSize, PurchaseSearchDto? searchDto = null);

        // Statistics
        Task<object> GetPurchaseStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }
}

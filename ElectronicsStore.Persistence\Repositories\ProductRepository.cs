using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Persistence.Repositories
{
    public class ProductRepository : GenericRepository<Product>, IProductRepository
    {
        public ProductRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<Product?> GetByBarcodeAsync(string barcode)
        {
            return await _dbSet.FirstOrDefaultAsync(p => p.Barcode == barcode);
        }

        public async Task<Product?> GetWithCategoryAsync(int id)
        {
            return await _dbSet
                .Include(p => p.Category)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Product?> GetWithCategoryAndSupplierAsync(int id)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Product>> GetByCategoryAsync(int categoryId)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Where(p => p.CategoryId == categoryId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetBySupplierAsync(int supplierId)
        {
            return await _dbSet
                .Include(p => p.Supplier)
                .Where(p => p.SupplierId == supplierId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .Where(p => p.Name.Contains(searchTerm) || 
                           (p.Barcode != null && p.Barcode.Contains(searchTerm)) ||
                           p.Description!.Contains(searchTerm))
                .ToListAsync();
        }

        public async Task<bool> IsBarcodeUniqueAsync(string barcode, int? excludeId = null)
        {
            var query = _dbSet.Where(p => p.Barcode == barcode);
            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);
            
            return !await query.AnyAsync();
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10)
        {
            // This would typically join with inventory view or calculate from inventory logs
            var lowStockProducts = await _context.InventoryViews
                .Where(iv => iv.CurrentQuantity <= threshold)
                .Select(iv => iv.ProductId)
                .ToListAsync();

            return await _dbSet
                .Include(p => p.Category)
                .Where(p => lowStockProducts.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOutOfStockProductsAsync()
        {
            var outOfStockProducts = await _context.InventoryViews
                .Where(iv => iv.CurrentQuantity <= 0)
                .Select(iv => iv.ProductId)
                .ToListAsync();

            return await _dbSet
                .Include(p => p.Category)
                .Where(p => outOfStockProducts.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<Dictionary<int, int>> GetProductsCurrentStockAsync(IEnumerable<int> productIds)
        {
            return await _context.InventoryViews
                .Where(iv => productIds.Contains(iv.ProductId))
                .ToDictionaryAsync(iv => iv.ProductId, iv => iv.CurrentQuantity);
        }

        public async Task<int> GetProductCurrentStockAsync(int productId)
        {
            var inventoryView = await _context.InventoryViews
                .FirstOrDefaultAsync(iv => iv.ProductId == productId);
            
            return inventoryView?.CurrentQuantity ?? 0;
        }

        public async Task<IEnumerable<Product>> GetProductsByPriceRangeAsync(decimal minPrice, decimal maxPrice)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Where(p => p.DefaultSellingPrice >= minPrice && p.DefaultSellingPrice <= maxPrice)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetProductsBelowMinPriceAsync()
        {
            return await _dbSet
                .Include(p => p.Category)
                .Where(p => p.DefaultSellingPrice < p.MinSellingPrice)
                .ToListAsync();
        }

        public async Task<bool> UpdateStockAsync(int productId, int quantity, string reason, int userId)
        {
            // This would create an inventory log entry
            var inventoryLog = new InventoryLog
            {
                ProductId = productId,
                MovementType = "adjust",
                Quantity = quantity,
                UnitCost = 0, // Would need to get current cost
                ReferenceTable = "manual_adjustment",
                ReferenceId = 0,
                Note = reason,
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            await _context.InventoryLogs.AddAsync(inventoryLog);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> AdjustStockAsync(int productId, int newQuantity, string reason, int userId)
        {
            var currentStock = await GetProductCurrentStockAsync(productId);
            var adjustment = newQuantity - currentStock;
            
            return await UpdateStockAsync(productId, adjustment, reason, userId);
        }

        public async Task<bool> HasSufficientStockAsync(int productId, int requiredQuantity)
        {
            var currentStock = await GetProductCurrentStockAsync(productId);
            return currentStock >= requiredQuantity;
        }

        public async Task<int> GetTotalProductCountAsync()
        {
            return await _dbSet.CountAsync();
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            return await _context.InventoryValuationViews.SumAsync(iv => iv.TotalValue);
        }

        public async Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoiceDetails.AsQueryable();
            
            if (fromDate.HasValue)
                query = query.Where(sid => sid.SalesInvoice.InvoiceDate >= fromDate.Value);
            
            if (toDate.HasValue)
                query = query.Where(sid => sid.SalesInvoice.InvoiceDate <= toDate.Value);

            var topProductIds = await query
                .GroupBy(sid => sid.ProductId)
                .OrderByDescending(g => g.Sum(sid => sid.Quantity))
                .Take(count)
                .Select(g => g.Key)
                .ToListAsync();

            return await _dbSet
                .Include(p => p.Category)
                .Where(p => topProductIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetRecentlyAddedProductsAsync(int count = 10)
        {
            return await _dbSet
                .Include(p => p.Category)
                .OrderByDescending(p => p.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetProductStatisticsAsync()
        {
            var totalProducts = await _dbSet.CountAsync();
            var totalCategories = await _context.Categories.CountAsync();
            var totalValue = await GetTotalInventoryValueAsync();
            var lowStockCount = (await GetLowStockProductsAsync()).Count();
            var outOfStockCount = (await GetOutOfStockProductsAsync()).Count();

            return new Dictionary<string, object>
            {
                ["TotalProducts"] = totalProducts,
                ["TotalCategories"] = totalCategories,
                ["TotalInventoryValue"] = totalValue,
                ["LowStockProducts"] = lowStockCount,
                ["OutOfStockProducts"] = outOfStockCount
            };
        }

        public async Task<PagedResult<Product>> GetProductsWithStockAsync(int pageNumber, int pageSize, 
            Expression<Func<Product, bool>>? filter = null)
        {
            var query = _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .AsQueryable();

            if (filter != null)
                query = query.Where(filter);

            var totalCount = await query.CountAsync();
            var products = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            // Get stock information for these products
            var productIds = products.Select(p => p.Id).ToList();
            var stockInfo = await GetProductsCurrentStockAsync(productIds);

            return new PagedResult<Product>
            {
                Data = products,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<IEnumerable<Product>> GetProductsWithLowMarginAsync(decimal marginThreshold = 0.1m)
        {
            return await _dbSet
                .Include(p => p.Category)
                .Where(p => (p.DefaultSellingPrice - p.DefaultCostPrice) / p.DefaultSellingPrice < marginThreshold)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetFastMovingProductsAsync(int days = 30)
        {
            var fromDate = DateTime.Now.AddDays(-days);
            var fastMovingIds = await _context.SalesInvoiceDetails
                .Where(sid => sid.SalesInvoice.InvoiceDate >= fromDate)
                .GroupBy(sid => sid.ProductId)
                .Where(g => g.Sum(sid => sid.Quantity) > 50) // Threshold for fast moving
                .Select(g => g.Key)
                .ToListAsync();

            return await _dbSet
                .Include(p => p.Category)
                .Where(p => fastMovingIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetSlowMovingProductsAsync(int days = 90)
        {
            var fromDate = DateTime.Now.AddDays(-days);
            var soldProductIds = await _context.SalesInvoiceDetails
                .Where(sid => sid.SalesInvoice.InvoiceDate >= fromDate)
                .Select(sid => sid.ProductId)
                .Distinct()
                .ToListAsync();

            return await _dbSet
                .Include(p => p.Category)
                .Where(p => !soldProductIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<bool> HasActiveInventoryAsync(int productId)
        {
            return await _context.InventoryLogs.AnyAsync(il => il.ProductId == productId);
        }

        public async Task<bool> CanDeleteProductAsync(int productId)
        {
            var hasSales = await _context.SalesInvoiceDetails.AnyAsync(sid => sid.ProductId == productId);
            var hasPurchases = await _context.PurchaseInvoiceDetails.AnyAsync(pid => pid.ProductId == productId);
            var hasInventory = await HasActiveInventoryAsync(productId);

            return !hasSales && !hasPurchases && !hasInventory;
        }

        public async Task<bool> HasPendingOrdersAsync(int productId)
        {
            // This would check for pending purchase orders or sales orders
            // For now, we'll just return false as we don't have these entities yet
            return false;
        }

        public async Task<int> UpdateProductPricesAsync(int categoryId, decimal priceAdjustment, bool isPercentage = false)
        {
            var products = await _dbSet.Where(p => p.CategoryId == categoryId).ToListAsync();
            
            foreach (var product in products)
            {
                if (isPercentage)
                {
                    product.DefaultSellingPrice *= (1 + priceAdjustment / 100);
                    product.DefaultCostPrice *= (1 + priceAdjustment / 100);
                }
                else
                {
                    product.DefaultSellingPrice += priceAdjustment;
                    product.DefaultCostPrice += priceAdjustment;
                }
            }

            return await _context.SaveChangesAsync();
        }

        public async Task<int> UpdateProductPricesAsync(IEnumerable<int> productIds, decimal priceAdjustment, bool isPercentage = false)
        {
            var products = await _dbSet.Where(p => productIds.Contains(p.Id)).ToListAsync();
            
            foreach (var product in products)
            {
                if (isPercentage)
                {
                    product.DefaultSellingPrice *= (1 + priceAdjustment / 100);
                    product.DefaultCostPrice *= (1 + priceAdjustment / 100);
                }
                else
                {
                    product.DefaultSellingPrice += priceAdjustment;
                    product.DefaultCostPrice += priceAdjustment;
                }
            }

            return await _context.SaveChangesAsync();
        }

        public async Task<int> BulkUpdateCategoryAsync(IEnumerable<int> productIds, int newCategoryId)
        {
            var products = await _dbSet.Where(p => productIds.Contains(p.Id)).ToListAsync();
            
            foreach (var product in products)
            {
                product.CategoryId = newCategoryId;
            }

            return await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Product>> GetProductsNeedingReorderAsync()
        {
            // This would typically check against reorder levels
            // For now, we'll use low stock as a proxy
            return await GetLowStockProductsAsync(5);
        }

        public async Task<(IEnumerable<Product> Products, int TotalCount)> GetPagedProductsAsync(
            int pageNumber, int pageSize, string? searchTerm = null, int? categoryId = null, int? supplierId = null)
        {
            var query = _dbSet
                .Include(p => p.Category)
                .Include(p => p.Supplier)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(p => p.Name.Contains(searchTerm) || 
                                        (p.Barcode != null && p.Barcode.Contains(searchTerm)));
            }

            if (categoryId.HasValue)
                query = query.Where(p => p.CategoryId == categoryId.Value);

            if (supplierId.HasValue)
                query = query.Where(p => p.SupplierId == supplierId.Value);

            var totalCount = await query.CountAsync();
            var products = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (products, totalCount);
        }
    }
}

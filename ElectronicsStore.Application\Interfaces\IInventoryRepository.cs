using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IInventoryRepository : IGenericRepository<InventoryLog>
    {
        // Inventory movement queries
        Task<IEnumerable<InventoryLog>> GetByProductAsync(int productId);
        Task<IEnumerable<InventoryLog>> GetByMovementTypeAsync(string movementType);
        Task<IEnumerable<InventoryLog>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<InventoryLog>> GetByUserAsync(int userId);
        Task<IEnumerable<InventoryLog>> GetByReferenceAsync(string referenceTable, int referenceId);

        // Stock calculations
        Task<int> GetCurrentStockAsync(int productId);
        Task<Dictionary<int, int>> GetCurrentStockForProductsAsync(IEnumerable<int> productIds);
        Task<decimal> GetCurrentStockValueAsync(int productId);
        Task<decimal> GetTotalInventoryValueAsync();

        // Inventory analytics
        Task<IEnumerable<object>> GetInventoryMovementSummaryAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<object>> GetStockLevelsReportAsync();
        Task<IEnumerable<object>> GetInventoryTurnoverAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<Product>> GetDeadStockAsync(int daysThreshold = 180);

        // Stock adjustments
        Task<bool> CreateStockAdjustmentAsync(int productId, int newQuantity, string reason, int userId);
        Task<bool> CreateStockMovementAsync(int productId, int quantity, string movementType, 
            string referenceTable, int referenceId, decimal unitCost, string? note, int userId);

        // Inventory validation
        Task<bool> HasSufficientStockAsync(int productId, int requiredQuantity);
        Task<Dictionary<int, bool>> ValidateStockAvailabilityAsync(Dictionary<int, int> productQuantities);
        Task<bool> CanReduceStockAsync(int productId, int quantity);

        // Low stock alerts
        Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10);
        Task<IEnumerable<Product>> GetOutOfStockProductsAsync();
        Task<IEnumerable<Product>> GetOverstockProductsAsync(int threshold = 1000);

        // Inventory reports
        Task<IEnumerable<object>> GetInventoryAgeingReportAsync();
        Task<IEnumerable<object>> GetSlowMovingItemsAsync(int daysThreshold = 90);
        Task<IEnumerable<object>> GetFastMovingItemsAsync(int daysThreshold = 30);
        Task<IEnumerable<object>> GetInventoryValuationReportAsync();

        // Advanced queries
        Task<PagedResult<InventoryLog>> GetInventoryHistoryAsync(int pageNumber, int pageSize, 
            int? productId = null, string? movementType = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<InventoryLog>> GetRecentMovementsAsync(int count = 50);
        Task<IEnumerable<object>> GetInventoryTrendAsync(int productId, int days = 30);

        // Bulk operations
        Task<int> BulkStockAdjustmentAsync(Dictionary<int, int> productStockAdjustments, string reason, int userId);
        Task<bool> ProcessSaleStockMovementAsync(IEnumerable<SalesInvoiceDetail> saleDetails, int salesInvoiceId, int userId);
        Task<bool> ProcessPurchaseStockMovementAsync(IEnumerable<PurchaseInvoiceDetail> purchaseDetails, int purchaseInvoiceId, int userId);
        Task<bool> ProcessReturnStockMovementAsync(int productId, int quantity, string returnType, int referenceId, int userId);
    }
}

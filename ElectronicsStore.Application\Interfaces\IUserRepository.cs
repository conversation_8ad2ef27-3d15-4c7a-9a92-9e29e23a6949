using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IUserRepository : IGenericRepository<User>
    {
        // User-specific queries
        Task<User?> GetByUsernameAsync(string username);
        Task<User?> GetWithRoleAsync(int id);
        Task<User?> GetWithRoleByUsernameAsync(string username);
        Task<IEnumerable<User>> GetByRoleAsync(int roleId);
        Task<IEnumerable<User>> GetByRoleNameAsync(string roleName);
        Task<IEnumerable<User>> GetActiveUsersAsync();

        // Authentication and security
        Task<bool> ValidateCredentialsAsync(string username, string password);
        Task<bool> IsUsernameUniqueAsync(string username, int? excludeId = null);
        Task<bool> UpdatePasswordAsync(int userId, string newPasswordHash);
        Task<DateTime?> GetLastLoginAsync(int userId);
        Task<bool> UpdateLastLoginAsync(int userId);

        // User activity tracking
        Task<IEnumerable<object>> GetUserActivityAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<User>> GetMostActiveUsersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetUserSalesCountAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetUserSalesTotalAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);

        // User management
        Task<IEnumerable<User>> GetUsersByCreationDateAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<User>> GetRecentlyCreatedUsersAsync(int count = 10);
        Task<bool> CanDeleteUserAsync(int userId);
        Task<bool> HasUserMadeTransactionsAsync(int userId);

        // Role and permissions
        Task<IEnumerable<string>> GetUserPermissionsAsync(int userId);
        Task<bool> HasPermissionAsync(int userId, string permissionName);
        Task<IEnumerable<User>> GetUsersByPermissionAsync(string permissionName);

        // User statistics
        Task<Dictionary<string, int>> GetUserCountByRoleAsync();
        Task<IEnumerable<object>> GetUserPerformanceReportAsync(DateTime fromDate, DateTime toDate);
        Task<int> GetTotalActiveUsersAsync();

        // Advanced queries
        Task<PagedResult<User>> GetPagedUsersWithRolesAsync(int pageNumber, int pageSize, 
            Expression<Func<User, bool>>? filter = null);
        Task<IEnumerable<User>> SearchUsersAsync(string searchTerm);

        // Pagination with enhanced features
        Task<(IEnumerable<User> Users, int TotalCount)> GetPagedUsersAsync(
            int pageNumber, int pageSize, string? searchTerm = null, int? roleId = null, 
            DateTime? fromDate = null, DateTime? toDate = null);

        // Validation
        Task<bool> IsUserActiveAsync(int userId);
        Task<bool> CanUserPerformActionAsync(int userId, string action);
    }
}

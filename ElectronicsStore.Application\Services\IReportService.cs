using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IReportService
    {
        // Financial Reports
        Task<object> GetFinancialSummaryAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<object> GetProfitLossReportAsync(DateTime fromDate, DateTime toDate);
        Task<object> GetCashFlowReportAsync(DateTime fromDate, DateTime toDate);

        // Sales Reports
        Task<IEnumerable<SalesReportDto>> GetSalesReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<TopCustomerDto>> GetTopCustomersReportAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<TopProductDto>> GetTopProductsReportAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<object> GetSalesPerformanceReportAsync(DateTime fromDate, DateTime toDate);

        // Purchase Reports
        Task<object> GetPurchaseReportAsync(DateTime fromDate, DateTime toDate);
        Task<object> GetTopSuppliersReportAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<object> GetPurchaseAnalysisReportAsync(DateTime fromDate, DateTime toDate);

        // Inventory Reports
        Task<IEnumerable<InventoryValuationDto>> GetInventoryValuationReportAsync();
        Task<IEnumerable<LowStockAlertDto>> GetStockAlertsReportAsync();
        Task<object> GetInventoryMovementReportAsync(DateTime fromDate, DateTime toDate);
        Task<object> GetSlowMovingProductsReportAsync(int days = 90);

        // Expense Reports
        Task<IEnumerable<ExpenseReportDto>> GetExpenseReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<ExpenseCategoryDto>> GetExpenseCategoryReportAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Dashboard Reports
        Task<object> GetDashboardSummaryAsync();
        Task<object> GetDashboardChartsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<object> GetKPIReportAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Comparative Reports
        Task<object> GetYearOverYearComparisonAsync(int year1, int year2);
        Task<object> GetMonthOverMonthComparisonAsync(int year, int month1, int month2);
        Task<object> GetPeriodComparisonAsync(DateTime period1Start, DateTime period1End, DateTime period2Start, DateTime period2End);

        // User Performance Reports
        Task<object> GetUserPerformanceReportAsync(DateTime fromDate, DateTime toDate);
        Task<object> GetUserSalesReportAsync(int userId, DateTime fromDate, DateTime toDate);

        // Custom Reports
        Task<object> GetCustomReportAsync(string reportType, Dictionary<string, object> parameters);
        Task<byte[]> ExportReportToPdfAsync(string reportType, Dictionary<string, object> parameters);
        Task<byte[]> ExportReportToExcelAsync(string reportType, Dictionary<string, object> parameters);
    }
}

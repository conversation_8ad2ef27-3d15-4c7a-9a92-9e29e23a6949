using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Persistence.Repositories
{
    public class SalesRepository : GenericRepository<SalesInvoice>, ISalesRepository
    {
        public SalesRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<SalesInvoice?> GetWithDetailsAsync(int id)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Include(si => si.User)
                .FirstOrDefaultAsync(si => si.Id == id);
        }

        public async Task<SalesInvoice?> GetWithDetailsAndProductsAsync(int id)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                    .ThenInclude(sid => sid.Product)
                .Include(si => si.User)
                .Include(si => si.OverrideByUser)
                .FirstOrDefaultAsync(si => si.Id == id);
        }

        public async Task<SalesInvoice?> GetByInvoiceNumberAsync(string invoiceNumber)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Include(si => si.User)
                .FirstOrDefaultAsync(si => si.InvoiceNumber == invoiceNumber);
        }

        public async Task<IEnumerable<SalesInvoice>> GetByUserAsync(int userId)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Where(si => si.UserId == userId)
                .OrderByDescending(si => si.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetByCustomerAsync(string customerName)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Where(si => si.CustomerName == customerName)
                .OrderByDescending(si => si.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Include(si => si.User)
                .Where(si => si.InvoiceDate >= fromDate && si.InvoiceDate <= toDate)
                .OrderByDescending(si => si.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetTodaysSalesAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Include(si => si.User)
                .Where(si => si.InvoiceDate >= today && si.InvoiceDate < tomorrow)
                .OrderByDescending(si => si.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetByPaymentMethodAsync(string paymentMethod)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Where(si => si.PaymentMethod == paymentMethod)
                .OrderByDescending(si => si.InvoiceDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalSalesAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query.SumAsync(si => si.TotalAmount);
        }

        public async Task<int> GetTotalSalesCountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query.CountAsync();
        }

        public async Task<decimal> GetAverageSaleAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query.AverageAsync(si => si.TotalAmount);
        }

        public async Task<decimal> GetTotalDiscountAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query.SumAsync(si => si.DiscountTotal);
        }

        public async Task<decimal> GetTotalProfitAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            // This would require joining with COGS view or calculating from cost data
            var cogsQuery = _context.CogsViews.AsQueryable();
            var salesQuery = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                salesQuery = salesQuery.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                salesQuery = salesQuery.Where(si => si.InvoiceDate <= toDate.Value);

            var totalSales = await salesQuery.SumAsync(si => si.TotalAmount);
            var totalCogs = await cogsQuery
                .Where(cv => salesQuery.Any(si => si.Id == cv.SalesInvoiceId))
                .SumAsync(cv => cv.CostOfGoodsSold);

            return totalSales - totalCogs;
        }

        public async Task<IEnumerable<object>> GetDailySalesReportAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Where(si => si.InvoiceDate >= fromDate && si.InvoiceDate <= toDate)
                .GroupBy(si => si.InvoiceDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount),
                    TotalDiscount = g.Sum(si => si.DiscountTotal),
                    AverageAmount = g.Average(si => si.TotalAmount)
                })
                .OrderBy(x => x.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetMonthlySalesReportAsync(int year)
        {
            return await _dbSet
                .Where(si => si.InvoiceDate.Year == year)
                .GroupBy(si => si.InvoiceDate.Month)
                .Select(g => new
                {
                    Month = g.Key,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount),
                    TotalDiscount = g.Sum(si => si.DiscountTotal),
                    AverageAmount = g.Average(si => si.TotalAmount)
                })
                .OrderBy(x => x.Month)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetYearlySalesReportAsync()
        {
            return await _dbSet
                .GroupBy(si => si.InvoiceDate.Year)
                .Select(g => new
                {
                    Year = g.Key,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount),
                    TotalDiscount = g.Sum(si => si.DiscountTotal),
                    AverageAmount = g.Average(si => si.TotalAmount)
                })
                .OrderBy(x => x.Year)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.Where(si => !string.IsNullOrEmpty(si.CustomerName));

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(si => si.CustomerName)
                .Select(g => new
                {
                    CustomerName = g.Key,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount),
                    AverageAmount = g.Average(si => si.TotalAmount),
                    LastSaleDate = g.Max(si => si.InvoiceDate)
                })
                .OrderByDescending(x => x.TotalAmount)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetSalesByPaymentMethodAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(si => si.PaymentMethod)
                .Select(g => new
                {
                    PaymentMethod = g.Key,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount),
                    Percentage = (decimal)g.Count() / query.Count() * 100
                })
                .OrderByDescending(x => x.TotalAmount)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetSalesByUserAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.Include(si => si.User).AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(si => new { si.UserId, si.User.Username })
                .Select(g => new
                {
                    UserId = g.Key.UserId,
                    Username = g.Key.Username,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount),
                    AverageAmount = g.Average(si => si.TotalAmount)
                })
                .OrderByDescending(x => x.TotalAmount)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetHourlySalesAsync(DateTime date)
        {
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            return await _dbSet
                .Where(si => si.InvoiceDate >= startDate && si.InvoiceDate < endDate)
                .GroupBy(si => si.InvoiceDate.Hour)
                .Select(g => new
                {
                    Hour = g.Key,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount)
                })
                .OrderBy(x => x.Hour)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetSalesTrendsAsync(int days = 30)
        {
            var fromDate = DateTime.Today.AddDays(-days);

            return await _dbSet
                .Where(si => si.InvoiceDate >= fromDate)
                .GroupBy(si => si.InvoiceDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    SalesCount = g.Count(),
                    TotalAmount = g.Sum(si => si.TotalAmount)
                })
                .OrderBy(x => x.Date)
                .ToListAsync();
        }

        public async Task<Dictionary<string, decimal>> GetSalesKPIsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            var kpis = new Dictionary<string, decimal>();
            
            if (await query.AnyAsync())
            {
                kpis["TotalSales"] = await query.SumAsync(si => si.TotalAmount);
                kpis["TotalTransactions"] = await query.CountAsync();
                kpis["AverageTransactionValue"] = await query.AverageAsync(si => si.TotalAmount);
                kpis["TotalDiscount"] = await query.SumAsync(si => si.DiscountTotal);
                kpis["DiscountPercentage"] = kpis["TotalDiscount"] / kpis["TotalSales"] * 100;
            }

            return kpis;
        }

        public async Task<IEnumerable<SalesInvoice>> GetLargeSalesAsync(decimal minimumAmount)
        {
            return await _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Include(si => si.User)
                .Where(si => si.TotalAmount >= minimumAmount)
                .OrderByDescending(si => si.TotalAmount)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetSalesWithDiscountsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet
                .Include(si => si.SalesInvoiceDetails)
                .Where(si => si.DiscountTotal > 0);

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query
                .OrderByDescending(si => si.DiscountTotal)
                .ToListAsync();
        }

        public async Task<bool> IsInvoiceNumberUniqueAsync(string invoiceNumber, int? excludeId = null)
        {
            var query = _dbSet.Where(si => si.InvoiceNumber == invoiceNumber);
            
            if (excludeId.HasValue)
                query = query.Where(si => si.Id != excludeId.Value);

            return !await query.AnyAsync();
        }

        public async Task<string> GenerateNextInvoiceNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"INV-{today:yyyyMMdd}-";
            
            var lastInvoice = await _dbSet
                .Where(si => si.InvoiceNumber.StartsWith(prefix))
                .OrderByDescending(si => si.InvoiceNumber)
                .FirstOrDefaultAsync();

            if (lastInvoice == null)
                return $"{prefix}001";

            var lastNumber = lastInvoice.InvoiceNumber.Substring(prefix.Length);
            if (int.TryParse(lastNumber, out int number))
                return $"{prefix}{(number + 1):D3}";

            return $"{prefix}001";
        }

        public async Task<IEnumerable<SalesInvoice>> GetOverriddenSalesAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet
                .Include(si => si.OverrideByUser)
                .Where(si => si.OverrideByUserId != null);

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query
                .OrderByDescending(si => si.OverrideDate)
                .ToListAsync();
        }

        public async Task<bool> CanCancelInvoiceAsync(int invoiceId)
        {
            var invoice = await GetByIdAsync(invoiceId);
            if (invoice == null)
                return false;

            // Check if there are any returns for this invoice
            var hasReturns = await _context.SalesReturns.AnyAsync(sr => sr.SalesInvoiceId == invoiceId);
            
            // Check if invoice is older than allowed cancellation period (e.g., 24 hours)
            var cancellationDeadline = DateTime.Now.AddHours(-24);
            var isWithinCancellationPeriod = invoice.InvoiceDate > cancellationDeadline;

            return !hasReturns && isWithinCancellationPeriod;
        }

        public async Task<IEnumerable<SalesReturn>> GetSalesReturnsAsync(int salesInvoiceId)
        {
            return await _context.SalesReturns
                .Include(sr => sr.Product)
                .Include(sr => sr.User)
                .Where(sr => sr.SalesInvoiceId == salesInvoiceId)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalReturnsAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesReturns
                .Include(sr => sr.SalesInvoice)
                .Include(sr => sr.Product)
                .AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(sr => sr.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(sr => sr.CreatedAt <= toDate.Value);

            // Calculate return amount based on original sale price
            return await query
                .Join(_context.SalesInvoiceDetails,
                    sr => new { sr.SalesInvoiceId, sr.ProductId },
                    sid => new { SalesInvoiceId = sid.SalesInvoiceId, ProductId = sid.ProductId },
                    (sr, sid) => new { sr.Quantity, sid.UnitPrice })
                .SumAsync(x => x.Quantity * x.UnitPrice);
        }

        public async Task<IEnumerable<object>> GetReturnReasonsReportAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesReturns.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(sr => sr.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(sr => sr.CreatedAt <= toDate.Value);

            return await query
                .GroupBy(sr => sr.Reason ?? "No Reason")
                .Select(g => new
                {
                    Reason = g.Key,
                    Count = g.Count(),
                    TotalQuantity = g.Sum(sr => sr.Quantity)
                })
                .OrderByDescending(x => x.Count)
                .ToListAsync();
        }

        public async Task<PagedResult<SalesInvoice>> GetPagedSalesWithDetailsAsync(int pageNumber, int pageSize, 
            Expression<Func<SalesInvoice, bool>>? filter = null)
        {
            var query = _dbSet
                .Include(si => si.SalesInvoiceDetails)
                    .ThenInclude(sid => sid.Product)
                .Include(si => si.User)
                .AsQueryable();

            if (filter != null)
                query = query.Where(filter);

            var totalCount = await query.CountAsync();
            var sales = await query
                .OrderByDescending(si => si.InvoiceDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<SalesInvoice>
            {
                Data = sales,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<(IEnumerable<SalesInvoice> Sales, int TotalCount)> GetPagedSalesAsync(
            int pageNumber, int pageSize, string? searchTerm = null, DateTime? fromDate = null, DateTime? toDate = null, 
            string? paymentMethod = null, int? userId = null)
        {
            var query = _dbSet
                .Include(si => si.User)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(si => si.InvoiceNumber.Contains(searchTerm) || 
                                         (si.CustomerName != null && si.CustomerName.Contains(searchTerm)));
            }

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            if (!string.IsNullOrEmpty(paymentMethod))
                query = query.Where(si => si.PaymentMethod == paymentMethod);

            if (userId.HasValue)
                query = query.Where(si => si.UserId == userId.Value);

            var totalCount = await query.CountAsync();
            var sales = await query
                .OrderByDescending(si => si.InvoiceDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (sales, totalCount);
        }

        public async Task<bool> HasValidStockForSaleAsync(IEnumerable<SalesInvoiceDetail> details)
        {
            foreach (var detail in details)
            {
                var currentStock = await _context.InventoryViews
                    .Where(iv => iv.ProductId == detail.ProductId)
                    .Select(iv => iv.CurrentQuantity)
                    .FirstOrDefaultAsync();

                if (currentStock < detail.Quantity)
                    return false;
            }

            return true;
        }

        public async Task<bool> ValidateMinimumPricesAsync(IEnumerable<SalesInvoiceDetail> details, int? overrideUserId = null)
        {
            foreach (var detail in details)
            {
                var product = await _context.Products.FindAsync(detail.ProductId);
                if (product == null)
                    return false;

                // If price is below minimum and no override user, validation fails
                if (detail.UnitPrice < product.MinSellingPrice && !overrideUserId.HasValue)
                    return false;
            }

            return true;
        }
    }
}

using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Persistence.Repositories
{
    public class InventoryRepository : GenericRepository<InventoryLog>, IInventoryRepository
    {
        public InventoryRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<InventoryLog>> GetByProductAsync(int productId)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.ProductId == productId)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetByMovementTypeAsync(string movementType)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.MovementType == movementType)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.CreatedAt >= fromDate && il.CreatedAt <= toDate)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetByUserAsync(int userId)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.UserId == userId)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryLog>> GetByReferenceAsync(string referenceTable, int referenceId)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .Where(il => il.ReferenceTable == referenceTable && il.ReferenceId == referenceId)
                .OrderByDescending(il => il.CreatedAt)
                .ToListAsync();
        }

        public async Task<int> GetCurrentStockAsync(int productId)
        {
            var inventoryView = await _context.InventoryViews
                .FirstOrDefaultAsync(iv => iv.ProductId == productId);
            
            return inventoryView?.CurrentQuantity ?? 0;
        }

        public async Task<Dictionary<int, int>> GetCurrentStockForProductsAsync(IEnumerable<int> productIds)
        {
            return await _context.InventoryViews
                .Where(iv => productIds.Contains(iv.ProductId))
                .ToDictionaryAsync(iv => iv.ProductId, iv => iv.CurrentQuantity);
        }

        public async Task<decimal> GetCurrentStockValueAsync(int productId)
        {
            var inventoryValuation = await _context.InventoryValuationViews
                .FirstOrDefaultAsync(ivv => ivv.ProductId == productId);
            
            return inventoryValuation?.TotalValue ?? 0;
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            return await _context.InventoryValuationViews.SumAsync(ivv => ivv.TotalValue);
        }

        public async Task<IEnumerable<object>> GetInventoryMovementSummaryAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Where(il => il.CreatedAt >= fromDate && il.CreatedAt <= toDate)
                .GroupBy(il => new { il.MovementType, il.CreatedAt.Date })
                .Select(g => new
                {
                    Date = g.Key.Date,
                    MovementType = g.Key.MovementType,
                    TotalQuantity = g.Sum(il => il.Quantity),
                    TotalValue = g.Sum(il => il.Quantity * il.UnitCost),
                    TransactionCount = g.Count()
                })
                .OrderBy(x => x.Date)
                .ThenBy(x => x.MovementType)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetStockLevelsReportAsync()
        {
            return await _context.InventoryViews
                .Join(_context.Products.Include(p => p.Category),
                    iv => iv.ProductId,
                    p => p.Id,
                    (iv, p) => new
                    {
                        ProductId = p.Id,
                        ProductName = p.Name,
                        CategoryName = p.Category.Name,
                        CurrentQuantity = iv.CurrentQuantity,
                        Status = iv.CurrentQuantity <= 0 ? "Out of Stock" :
                                iv.CurrentQuantity <= 10 ? "Low Stock" : "In Stock"
                    })
                .OrderBy(x => x.CurrentQuantity)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetInventoryTurnoverAsync(DateTime fromDate, DateTime toDate)
        {
            var salesData = await _context.SalesInvoiceDetails
                .Include(sid => sid.SalesInvoice)
                .Include(sid => sid.Product)
                .Where(sid => sid.SalesInvoice.InvoiceDate >= fromDate && sid.SalesInvoice.InvoiceDate <= toDate)
                .GroupBy(sid => sid.ProductId)
                .Select(g => new
                {
                    ProductId = g.Key,
                    ProductName = g.First().Product.Name,
                    TotalSold = g.Sum(sid => sid.Quantity),
                    SalesValue = g.Sum(sid => sid.LineTotal)
                })
                .ToListAsync();

            var currentStock = await GetCurrentStockForProductsAsync(salesData.Select(s => s.ProductId));

            return salesData.Select(s => new
            {
                s.ProductId,
                s.ProductName,
                s.TotalSold,
                s.SalesValue,
                CurrentStock = currentStock.GetValueOrDefault(s.ProductId, 0),
                TurnoverRatio = currentStock.GetValueOrDefault(s.ProductId, 0) > 0 ? 
                    (double)s.TotalSold / currentStock[s.ProductId] : 0
            }).OrderByDescending(x => x.TurnoverRatio);
        }

        public async Task<IEnumerable<Product>> GetDeadStockAsync(int daysThreshold = 180)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysThreshold);
            
            var recentlyMovedProducts = await _dbSet
                .Where(il => il.CreatedAt >= cutoffDate && 
                           (il.MovementType == "sale" || il.MovementType == "adjust"))
                .Select(il => il.ProductId)
                .Distinct()
                .ToListAsync();

            return await _context.Products
                .Include(p => p.Category)
                .Where(p => !recentlyMovedProducts.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<bool> CreateStockAdjustmentAsync(int productId, int newQuantity, string reason, int userId)
        {
            var currentStock = await GetCurrentStockAsync(productId);
            var adjustment = newQuantity - currentStock;

            var inventoryLog = new InventoryLog
            {
                ProductId = productId,
                MovementType = "adjust",
                Quantity = adjustment,
                UnitCost = 0, // For adjustments, we might not have a specific cost
                ReferenceTable = "manual_adjustment",
                ReferenceId = 0,
                Note = reason,
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            await _dbSet.AddAsync(inventoryLog);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> CreateStockMovementAsync(int productId, int quantity, string movementType, 
            string referenceTable, int referenceId, decimal unitCost, string? note, int userId)
        {
            var inventoryLog = new InventoryLog
            {
                ProductId = productId,
                MovementType = movementType,
                Quantity = quantity,
                UnitCost = unitCost,
                ReferenceTable = referenceTable,
                ReferenceId = referenceId,
                Note = note,
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            await _dbSet.AddAsync(inventoryLog);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> HasSufficientStockAsync(int productId, int requiredQuantity)
        {
            var currentStock = await GetCurrentStockAsync(productId);
            return currentStock >= requiredQuantity;
        }

        public async Task<Dictionary<int, bool>> ValidateStockAvailabilityAsync(Dictionary<int, int> productQuantities)
        {
            var result = new Dictionary<int, bool>();
            var currentStocks = await GetCurrentStockForProductsAsync(productQuantities.Keys);

            foreach (var item in productQuantities)
            {
                var currentStock = currentStocks.GetValueOrDefault(item.Key, 0);
                result[item.Key] = currentStock >= item.Value;
            }

            return result;
        }

        public async Task<bool> CanReduceStockAsync(int productId, int quantity)
        {
            return await HasSufficientStockAsync(productId, quantity);
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10)
        {
            var lowStockProductIds = await _context.InventoryViews
                .Where(iv => iv.CurrentQuantity <= threshold)
                .Select(iv => iv.ProductId)
                .ToListAsync();

            return await _context.Products
                .Include(p => p.Category)
                .Where(p => lowStockProductIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOutOfStockProductsAsync()
        {
            var outOfStockProductIds = await _context.InventoryViews
                .Where(iv => iv.CurrentQuantity <= 0)
                .Select(iv => iv.ProductId)
                .ToListAsync();

            return await _context.Products
                .Include(p => p.Category)
                .Where(p => outOfStockProductIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOverstockProductsAsync(int threshold = 1000)
        {
            var overstockProductIds = await _context.InventoryViews
                .Where(iv => iv.CurrentQuantity >= threshold)
                .Select(iv => iv.ProductId)
                .ToListAsync();

            return await _context.Products
                .Include(p => p.Category)
                .Where(p => overstockProductIds.Contains(p.Id))
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetInventoryAgeingReportAsync()
        {
            // This would require tracking batch/lot information
            // For now, we'll return a simplified version based on last purchase date
            return await _context.Products
                .Include(p => p.Category)
                .Select(p => new
                {
                    ProductId = p.Id,
                    ProductName = p.Name,
                    CategoryName = p.Category.Name,
                    LastPurchaseDate = _context.PurchaseInvoiceDetails
                        .Where(pid => pid.ProductId == p.Id)
                        .Max(pid => (DateTime?)pid.PurchaseInvoice.InvoiceDate),
                    DaysSinceLastPurchase = _context.PurchaseInvoiceDetails
                        .Where(pid => pid.ProductId == p.Id)
                        .Any() ? EF.Functions.DateDiffDay(
                            _context.PurchaseInvoiceDetails
                                .Where(pid => pid.ProductId == p.Id)
                                .Max(pid => pid.PurchaseInvoice.InvoiceDate),
                            DateTime.Now) : (int?)null
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetSlowMovingItemsAsync(int daysThreshold = 90)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysThreshold);
            
            var recentSales = await _context.SalesInvoiceDetails
                .Include(sid => sid.SalesInvoice)
                .Where(sid => sid.SalesInvoice.InvoiceDate >= cutoffDate)
                .GroupBy(sid => sid.ProductId)
                .Select(g => new { ProductId = g.Key, TotalSold = g.Sum(sid => sid.Quantity) })
                .ToListAsync();

            var allProducts = await _context.Products
                .Include(p => p.Category)
                .ToListAsync();

            return allProducts
                .GroupJoin(recentSales,
                    p => p.Id,
                    s => s.ProductId,
                    (p, sales) => new
                    {
                        ProductId = p.Id,
                        ProductName = p.Name,
                        CategoryName = p.Category.Name,
                        TotalSold = sales.FirstOrDefault()?.TotalSold ?? 0
                    })
                .Where(x => x.TotalSold <= 5) // Threshold for slow moving
                .OrderBy(x => x.TotalSold);
        }

        public async Task<IEnumerable<object>> GetFastMovingItemsAsync(int daysThreshold = 30)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysThreshold);
            
            return await _context.SalesInvoiceDetails
                .Include(sid => sid.SalesInvoice)
                .Include(sid => sid.Product)
                .Where(sid => sid.SalesInvoice.InvoiceDate >= cutoffDate)
                .GroupBy(sid => new { sid.ProductId, ProductName = sid.Product.Name, CategoryName = sid.Product.Category.Name })
                .Select(g => new
                {
                    ProductId = g.Key.ProductId,
                    ProductName = g.Key.ProductName,
                    CategoryName = g.Key.CategoryName,
                    TotalSold = g.Sum(sid => sid.Quantity),
                    SalesCount = g.Count()
                })
                .Where(x => x.TotalSold >= 50) // Threshold for fast moving
                .OrderByDescending(x => x.TotalSold)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetInventoryValuationReportAsync()
        {
            return await _context.InventoryValuationViews
                .Join(_context.Products.Include(p => p.Category),
                    ivv => ivv.ProductId,
                    p => p.Id,
                    (ivv, p) => new
                    {
                        ProductId = p.Id,
                        ProductName = p.Name,
                        CategoryName = p.Category.Name,
                        TotalValue = ivv.TotalValue,
                        CurrentQuantity = _context.InventoryViews
                            .Where(iv => iv.ProductId == p.Id)
                            .Select(iv => iv.CurrentQuantity)
                            .FirstOrDefault(),
                        AverageUnitCost = ivv.TotalValue / Math.Max(1, _context.InventoryViews
                            .Where(iv => iv.ProductId == p.Id)
                            .Select(iv => iv.CurrentQuantity)
                            .FirstOrDefault())
                    })
                .OrderByDescending(x => x.TotalValue)
                .ToListAsync();
        }

        public async Task<PagedResult<InventoryLog>> GetInventoryHistoryAsync(int pageNumber, int pageSize, 
            int? productId = null, string? movementType = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .AsQueryable();

            if (productId.HasValue)
                query = query.Where(il => il.ProductId == productId.Value);

            if (!string.IsNullOrEmpty(movementType))
                query = query.Where(il => il.MovementType == movementType);

            if (fromDate.HasValue)
                query = query.Where(il => il.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(il => il.CreatedAt <= toDate.Value);

            var totalCount = await query.CountAsync();
            var logs = await query
                .OrderByDescending(il => il.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<InventoryLog>
            {
                Data = logs,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<IEnumerable<InventoryLog>> GetRecentMovementsAsync(int count = 50)
        {
            return await _dbSet
                .Include(il => il.Product)
                .Include(il => il.User)
                .OrderByDescending(il => il.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetInventoryTrendAsync(int productId, int days = 30)
        {
            var fromDate = DateTime.Now.AddDays(-days);
            
            return await _dbSet
                .Where(il => il.ProductId == productId && il.CreatedAt >= fromDate)
                .GroupBy(il => il.CreatedAt.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    TotalMovement = g.Sum(il => il.Quantity),
                    MovementCount = g.Count(),
                    MovementTypes = g.Select(il => il.MovementType).Distinct().ToList()
                })
                .OrderBy(x => x.Date)
                .ToListAsync();
        }

        public async Task<int> BulkStockAdjustmentAsync(Dictionary<int, int> productStockAdjustments, string reason, int userId)
        {
            var logs = new List<InventoryLog>();
            
            foreach (var adjustment in productStockAdjustments)
            {
                var currentStock = await GetCurrentStockAsync(adjustment.Key);
                var adjustmentQuantity = adjustment.Value - currentStock;
                
                logs.Add(new InventoryLog
                {
                    ProductId = adjustment.Key,
                    MovementType = "adjust",
                    Quantity = adjustmentQuantity,
                    UnitCost = 0,
                    ReferenceTable = "bulk_adjustment",
                    ReferenceId = 0,
                    Note = reason,
                    UserId = userId,
                    CreatedAt = DateTime.Now
                });
            }

            await _dbSet.AddRangeAsync(logs);
            return await _context.SaveChangesAsync();
        }

        public async Task<bool> ProcessSaleStockMovementAsync(IEnumerable<SalesInvoiceDetail> saleDetails, int salesInvoiceId, int userId)
        {
            var logs = new List<InventoryLog>();
            
            foreach (var detail in saleDetails)
            {
                logs.Add(new InventoryLog
                {
                    ProductId = detail.ProductId,
                    MovementType = "sale",
                    Quantity = -detail.Quantity, // Negative for outgoing stock
                    UnitCost = detail.UnitPrice,
                    ReferenceTable = "sales_invoices",
                    ReferenceId = salesInvoiceId,
                    Note = $"Sale - Invoice #{salesInvoiceId}",
                    UserId = userId,
                    CreatedAt = DateTime.Now
                });
            }

            await _dbSet.AddRangeAsync(logs);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ProcessPurchaseStockMovementAsync(IEnumerable<PurchaseInvoiceDetail> purchaseDetails, int purchaseInvoiceId, int userId)
        {
            var logs = new List<InventoryLog>();
            
            foreach (var detail in purchaseDetails)
            {
                logs.Add(new InventoryLog
                {
                    ProductId = detail.ProductId,
                    MovementType = "purchase",
                    Quantity = detail.Quantity, // Positive for incoming stock
                    UnitCost = detail.UnitCost,
                    ReferenceTable = "purchase_invoices",
                    ReferenceId = purchaseInvoiceId,
                    Note = $"Purchase - Invoice #{purchaseInvoiceId}",
                    UserId = userId,
                    CreatedAt = DateTime.Now
                });
            }

            await _dbSet.AddRangeAsync(logs);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ProcessReturnStockMovementAsync(int productId, int quantity, string returnType, int referenceId, int userId)
        {
            var movementType = returnType == "sales_return" ? "return_sale" : "return_purchase";
            var adjustedQuantity = returnType == "sales_return" ? quantity : -quantity; // Sales return adds stock, purchase return reduces stock

            var log = new InventoryLog
            {
                ProductId = productId,
                MovementType = movementType,
                Quantity = adjustedQuantity,
                UnitCost = 0, // Would need to get from original transaction
                ReferenceTable = returnType == "sales_return" ? "sales_returns" : "purchase_returns",
                ReferenceId = referenceId,
                Note = $"{returnType} - ID #{referenceId}",
                UserId = userId,
                CreatedAt = DateTime.Now
            };

            await _dbSet.AddAsync(log);
            return await _context.SaveChangesAsync() > 0;
        }
    }
}

2025-08-07 03:07:16.262 +03:00 [INF] Executed DbCommand (45ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:07:16.406 +03:00 [INF] Executed DbCommand (2ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:07:16.415 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__role_Name_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = @__role_Name_0
2025-08-07 03:07:16.469 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'admin'
2025-08-07 03:07:16.481 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'manager'
2025-08-07 03:07:16.494 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[id], [r].[name]
FROM [roles] AS [r]
WHERE [r].[name] = N'cashier'
2025-08-07 03:07:16.507 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:07:16.563 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:07:16.569 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__userData_Username_0='?' (Size = 50)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[id], [u].[created_at], [u].[password], [u].[role_id], [u].[username]
FROM [users] AS [u]
WHERE [u].[username] = @__userData_Username_0
2025-08-07 03:07:16.595 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.609 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.616 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.621 +03:00 [INF] Executed DbCommand (0ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.627 +03:00 [INF] Executed DbCommand (1ms) [Parameters=[@__category_Name_0='?' (Size = 100)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [c].[id], [c].[name]
FROM [categories] AS [c]
WHERE [c].[name] = @__category_Name_0
2025-08-07 03:07:16.666 +03:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-08-07 03:07:16.765 +03:00 [INF] Now listening on: http://localhost:5226
2025-08-07 03:07:16.771 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-08-07 03:07:16.773 +03:00 [INF] Hosting environment: Development
2025-08-07 03:07:16.775 +03:00 [INF] Content root path: C:\Users\<USER>\OneDrive\Desktop\Final\first\ElectronicsStore.API

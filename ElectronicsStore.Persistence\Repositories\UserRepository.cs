using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;
using BCrypt.Net;

namespace ElectronicsStore.Persistence.Repositories
{
    public class UserRepository : GenericRepository<User>, IUserRepository
    {
        public UserRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<User?> GetByUsernameAsync(string username)
        {
            return await _dbSet.FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<User?> GetWithRoleAsync(int id)
        {
            return await _dbSet
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Id == id);
        }

        public async Task<User?> GetWithRoleByUsernameAsync(string username)
        {
            return await _dbSet
                .Include(u => u.Role)
                .FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<IEnumerable<User>> GetByRoleAsync(int roleId)
        {
            return await _dbSet
                .Include(u => u.Role)
                .Where(u => u.RoleId == roleId)
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetByRoleNameAsync(string roleName)
        {
            return await _dbSet
                .Include(u => u.Role)
                .Where(u => u.Role.Name == roleName)
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            return await _dbSet
                .Include(u => u.Role)
                .ToListAsync();
        }

        public async Task<bool> ValidateCredentialsAsync(string username, string password)
        {
            var user = await GetByUsernameAsync(username);
            if (user == null)
                return false;

            return BCrypt.Net.BCrypt.Verify(password, user.Password);
        }

        public async Task<bool> IsUsernameUniqueAsync(string username, int? excludeId = null)
        {
            var query = _dbSet.Where(u => u.Username == username);
            if (excludeId.HasValue)
                query = query.Where(u => u.Id != excludeId.Value);
            
            return !await query.AnyAsync();
        }

        public async Task<bool> UpdatePasswordAsync(int userId, string newPasswordHash)
        {
            var user = await GetByIdAsync(userId);
            if (user == null)
                return false;

            user.Password = newPasswordHash;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<DateTime?> GetLastLoginAsync(int userId)
        {
            // This would require a LastLogin field in the User entity
            return null;
        }

        public async Task<bool> UpdateLastLoginAsync(int userId)
        {
            // This would update the LastLogin field
            return true;
        }

        public async Task<IEnumerable<object>> GetUserActivityAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoices
                .Where(si => si.UserId == userId)
                .AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query
                .Select(si => new
                {
                    Type = "Sale",
                    Date = si.InvoiceDate,
                    Amount = si.TotalAmount,
                    Reference = si.InvoiceNumber
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetMostActiveUsersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoices.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            var activeUserIds = await query
                .GroupBy(si => si.UserId)
                .OrderByDescending(g => g.Count())
                .Take(count)
                .Select(g => g.Key)
                .ToListAsync();

            return await _dbSet
                .Include(u => u.Role)
                .Where(u => activeUserIds.Contains(u.Id))
                .ToListAsync();
        }

        public async Task<int> GetUserSalesCountAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoices.Where(si => si.UserId == userId);

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query.CountAsync();
        }

        public async Task<decimal> GetUserSalesTotalAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoices.Where(si => si.UserId == userId);

            if (fromDate.HasValue)
                query = query.Where(si => si.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(si => si.InvoiceDate <= toDate.Value);

            return await query.SumAsync(si => si.TotalAmount);
        }

        public async Task<IEnumerable<User>> GetUsersByCreationDateAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(u => u.Role)
                .Where(u => u.CreatedAt >= fromDate && u.CreatedAt <= toDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetRecentlyCreatedUsersAsync(int count = 10)
        {
            return await _dbSet
                .Include(u => u.Role)
                .OrderByDescending(u => u.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task<bool> CanDeleteUserAsync(int userId)
        {
            var hasSales = await _context.SalesInvoices.AnyAsync(si => si.UserId == userId);
            var hasPurchases = await _context.PurchaseInvoices.AnyAsync(pi => pi.UserId == userId);
            var hasInventoryLogs = await _context.InventoryLogs.AnyAsync(il => il.UserId == userId);

            return !hasSales && !hasPurchases && !hasInventoryLogs;
        }

        public async Task<bool> HasUserMadeTransactionsAsync(int userId)
        {
            var hasSales = await _context.SalesInvoices.AnyAsync(si => si.UserId == userId);
            var hasPurchases = await _context.PurchaseInvoices.AnyAsync(pi => pi.UserId == userId);

            return hasSales || hasPurchases;
        }

        public async Task<IEnumerable<string>> GetUserPermissionsAsync(int userId)
        {
            var user = await _dbSet
                .Include(u => u.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(u => u.Id == userId);

            return user?.Role.RolePermissions.Select(rp => rp.Permission.Name) ?? new List<string>();
        }

        public async Task<bool> HasPermissionAsync(int userId, string permissionName)
        {
            return await _context.Users
                .Where(u => u.Id == userId)
                .SelectMany(u => u.Role.RolePermissions)
                .AnyAsync(rp => rp.Permission.Name == permissionName);
        }

        public async Task<IEnumerable<User>> GetUsersByPermissionAsync(string permissionName)
        {
            return await _dbSet
                .Include(u => u.Role)
                .Where(u => u.Role.RolePermissions.Any(rp => rp.Permission.Name == permissionName))
                .ToListAsync();
        }

        public async Task<Dictionary<string, int>> GetUserCountByRoleAsync()
        {
            return await _dbSet
                .Include(u => u.Role)
                .GroupBy(u => u.Role.Name)
                .ToDictionaryAsync(g => g.Key, g => g.Count());
        }

        public async Task<IEnumerable<object>> GetUserPerformanceReportAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(u => u.Role)
                .Select(u => new
                {
                    UserId = u.Id,
                    Username = u.Username,
                    Role = u.Role.Name,
                    SalesCount = u.SalesInvoices.Count(si => si.InvoiceDate >= fromDate && si.InvoiceDate <= toDate),
                    SalesTotal = u.SalesInvoices
                        .Where(si => si.InvoiceDate >= fromDate && si.InvoiceDate <= toDate)
                        .Sum(si => si.TotalAmount)
                })
                .ToListAsync();
        }

        public async Task<int> GetTotalActiveUsersAsync()
        {
            return await _dbSet.CountAsync();
        }

        public async Task<PagedResult<User>> GetPagedUsersWithRolesAsync(int pageNumber, int pageSize, 
            Expression<Func<User, bool>>? filter = null)
        {
            var query = _dbSet.Include(u => u.Role).AsQueryable();

            if (filter != null)
                query = query.Where(filter);

            var totalCount = await query.CountAsync();
            var users = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<User>
            {
                Data = users,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<IEnumerable<User>> SearchUsersAsync(string searchTerm)
        {
            return await _dbSet
                .Include(u => u.Role)
                .Where(u => u.Username.Contains(searchTerm) || u.Role.Name.Contains(searchTerm))
                .ToListAsync();
        }

        public async Task<(IEnumerable<User> Users, int TotalCount)> GetPagedUsersAsync(
            int pageNumber, int pageSize, string? searchTerm = null, int? roleId = null, 
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.Include(u => u.Role).AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
                query = query.Where(u => u.Username.Contains(searchTerm));

            if (roleId.HasValue)
                query = query.Where(u => u.RoleId == roleId.Value);

            if (fromDate.HasValue)
                query = query.Where(u => u.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(u => u.CreatedAt <= toDate.Value);

            var totalCount = await query.CountAsync();
            var users = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (users, totalCount);
        }

        public async Task<bool> IsUserActiveAsync(int userId)
        {
            // Assuming all users are active since we don't have an IsActive field
            return await _dbSet.AnyAsync(u => u.Id == userId);
        }

        public async Task<bool> CanUserPerformActionAsync(int userId, string action)
        {
            // This would check user permissions for specific actions
            var permissions = await GetUserPermissionsAsync(userId);
            return permissions.Contains(action);
        }
    }
}

using System.Net;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace ElectronicsStore.API.Middleware
{
    public class GlobalExceptionHandlerMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<GlobalExceptionHandlerMiddleware> _logger;

        public GlobalExceptionHandlerMiddleware(RequestDelegate next, ILogger<GlobalExceptionHandlerMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unhandled exception occurred. Request: {Method} {Path}", 
                    context.Request.Method, context.Request.Path);

                await HandleExceptionAsync(context, ex);
            }
        }

        private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
        {
            context.Response.ContentType = "application/json";

            var response = new ErrorResponse();

            switch (exception)
            {
                case InvalidOperationException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = ex.Message;
                    response.Details = "Invalid operation requested.";
                    break;

                case ArgumentNullException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = "Required parameter is missing.";
                    response.Details = ex.ParamName;
                    break;

                case ArgumentException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = ex.Message;
                    response.Details = "Invalid argument provided.";
                    break;

                case UnauthorizedAccessException:
                    response.StatusCode = (int)HttpStatusCode.Unauthorized;
                    response.Message = "Unauthorized access.";
                    response.Details = "You don't have permission to access this resource.";
                    break;

                case KeyNotFoundException ex:
                    response.StatusCode = (int)HttpStatusCode.NotFound;
                    response.Message = "Resource not found.";
                    response.Details = ex.Message;
                    break;

                case DbUpdateConcurrencyException:
                    response.StatusCode = (int)HttpStatusCode.Conflict;
                    response.Message = "Concurrency conflict occurred.";
                    response.Details = "The record was modified by another user. Please refresh and try again.";
                    break;

                case DbUpdateException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = "Database operation failed.";
                    response.Details = GetDbUpdateExceptionMessage(ex);
                    break;

                case TimeoutException:
                    response.StatusCode = (int)HttpStatusCode.RequestTimeout;
                    response.Message = "Request timeout.";
                    response.Details = "The operation took too long to complete.";
                    break;

                case NotSupportedException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = "Operation not supported.";
                    response.Details = ex.Message;
                    break;

                case FormatException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = "Invalid format.";
                    response.Details = ex.Message;
                    break;

                case OverflowException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = "Numeric overflow.";
                    response.Details = ex.Message;
                    break;

                case DivideByZeroException:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = "Division by zero error.";
                    response.Details = "Cannot divide by zero.";
                    break;

                case JsonException ex:
                    response.StatusCode = (int)HttpStatusCode.BadRequest;
                    response.Message = "Invalid JSON format.";
                    response.Details = ex.Message;
                    break;

                default:
                    response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    response.Message = "An internal server error occurred.";
                    response.Details = "Please contact support if the problem persists.";
                    break;
            }

            context.Response.StatusCode = response.StatusCode;

            var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(jsonResponse);
        }

        private static string GetDbUpdateExceptionMessage(DbUpdateException ex)
        {
            if (ex.InnerException?.Message.Contains("UNIQUE constraint failed") == true)
            {
                return "A record with this information already exists.";
            }

            if (ex.InnerException?.Message.Contains("FOREIGN KEY constraint failed") == true)
            {
                return "Cannot perform this operation due to related data constraints.";
            }

            if (ex.InnerException?.Message.Contains("CHECK constraint failed") == true)
            {
                return "Data validation failed. Please check your input values.";
            }

            if (ex.InnerException?.Message.Contains("NOT NULL constraint failed") == true)
            {
                return "Required field is missing.";
            }

            return "Database operation failed. Please check your data and try again.";
        }
    }

    public class ErrorResponse
    {
        public int StatusCode { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Details { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string TraceId { get; set; } = Guid.NewGuid().ToString();
    }

    // Extension method to register the middleware
    public static class GlobalExceptionHandlerMiddlewareExtensions
    {
        public static IApplicationBuilder UseGlobalExceptionHandler(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<GlobalExceptionHandlerMiddleware>();
        }
    }
}

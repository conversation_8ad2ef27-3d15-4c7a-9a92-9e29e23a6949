{"version": 3, "targets": {"net8.0": {"ElectronicsStore.Application/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"ElectronicsStore.Domain": "1.0.0"}, "compile": {"bin/placeholder/ElectronicsStore.Application.dll": {}}, "runtime": {"bin/placeholder/ElectronicsStore.Application.dll": {}}}, "ElectronicsStore.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/ElectronicsStore.Domain.dll": {}}, "runtime": {"bin/placeholder/ElectronicsStore.Domain.dll": {}}}, "ElectronicsStore.Infrastructure/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"ElectronicsStore.Application": "1.0.0"}, "compile": {"bin/placeholder/ElectronicsStore.Infrastructure.dll": {}}, "runtime": {"bin/placeholder/ElectronicsStore.Infrastructure.dll": {}}}, "ElectronicsStore.Persistence/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"ElectronicsStore.Application": "1.0.0"}, "compile": {"bin/placeholder/ElectronicsStore.Persistence.dll": {}}, "runtime": {"bin/placeholder/ElectronicsStore.Persistence.dll": {}}}}}, "libraries": {"ElectronicsStore.Application/1.0.0": {"type": "project", "path": "../ElectronicsStore.Application/ElectronicsStore.Application.csproj", "msbuildProject": "../ElectronicsStore.Application/ElectronicsStore.Application.csproj"}, "ElectronicsStore.Domain/1.0.0": {"type": "project", "path": "../ElectronicsStore.Domain/ElectronicsStore.Domain.csproj", "msbuildProject": "../ElectronicsStore.Domain/ElectronicsStore.Domain.csproj"}, "ElectronicsStore.Infrastructure/1.0.0": {"type": "project", "path": "../ElectronicsStore.Infrastructure/ElectronicsStore.Infrastructure.csproj", "msbuildProject": "../ElectronicsStore.Infrastructure/ElectronicsStore.Infrastructure.csproj"}, "ElectronicsStore.Persistence/1.0.0": {"type": "project", "path": "../ElectronicsStore.Persistence/ElectronicsStore.Persistence.csproj", "msbuildProject": "../ElectronicsStore.Persistence/ElectronicsStore.Persistence.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["ElectronicsStore.Application >= 1.0.0", "ElectronicsStore.Infrastructure >= 1.0.0", "ElectronicsStore.Persistence >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.API\\ElectronicsStore.API.csproj", "projectName": "ElectronicsStore.API", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.API\\ElectronicsStore.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.Application\\ElectronicsStore.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.Application\\ElectronicsStore.Application.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.Infrastructure\\ElectronicsStore.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.Infrastructure\\ElectronicsStore.Infrastructure.csproj"}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.Persistence\\ElectronicsStore.Persistence.csproj": {"projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Final\\first\\ElectronicsStore.Persistence\\ElectronicsStore.Persistence.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.400\\RuntimeIdentifierGraph.json"}}}}
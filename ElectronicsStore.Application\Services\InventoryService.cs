using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly IUnitOfWork _unitOfWork;

        public InventoryService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<InventoryLogDto?> GetInventoryLogByIdAsync(int id)
        {
            var inventoryLog = await _unitOfWork.Inventory.GetByIdWithIncludeAsync(id, 
                il => il.Product, il => il.User);
            
            return inventoryLog != null ? MapToInventoryLogDto(inventoryLog) : null;
        }

        public async Task<IEnumerable<InventoryLogDto>> GetAllInventoryLogsAsync()
        {
            var inventoryLogs = await _unitOfWork.Inventory.GetWithIncludeAsync(
                il => il.Product, il => il.User);
            
            return inventoryLogs.Select(MapToInventoryLogDto);
        }

        public async Task<InventoryLogDto> CreateInventoryLogAsync(CreateInventoryLogDto createInventoryLogDto)
        {
            // Validation
            if (!await _unitOfWork.Products.AnyAsync(p => p.Id == createInventoryLogDto.ProductId))
                throw new InvalidOperationException($"Product with ID {createInventoryLogDto.ProductId} not found.");

            if (!await _unitOfWork.Users.AnyAsync(u => u.Id == createInventoryLogDto.UserId))
                throw new InvalidOperationException($"User with ID {createInventoryLogDto.UserId} not found.");

            var validMovementTypes = new[] { "purchase", "sale", "return_sale", "return_purchase", "adjust" };
            if (!validMovementTypes.Contains(createInventoryLogDto.MovementType.ToLower()))
                throw new InvalidOperationException("Invalid movement type.");

            // Check if adjustment would result in negative stock
            if (createInventoryLogDto.Quantity < 0)
            {
                var currentStock = await GetCurrentStockAsync(createInventoryLogDto.ProductId);
                if (currentStock + createInventoryLogDto.Quantity < 0)
                    throw new InvalidOperationException("Adjustment would result in negative stock.");
            }

            var inventoryLog = new InventoryLog
            {
                ProductId = createInventoryLogDto.ProductId,
                MovementType = createInventoryLogDto.MovementType.ToLower(),
                Quantity = createInventoryLogDto.Quantity,
                UnitCost = createInventoryLogDto.UnitCost,
                ReferenceTable = createInventoryLogDto.ReferenceTable,
                ReferenceId = createInventoryLogDto.ReferenceId,
                Note = createInventoryLogDto.Note,
                UserId = createInventoryLogDto.UserId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.Inventory.AddAsync(inventoryLog);
            await _unitOfWork.SaveChangesAsync();

            // Get the created inventory log with includes
            var createdLog = await _unitOfWork.Inventory.GetByIdWithIncludeAsync(inventoryLog.Id, 
                il => il.Product, il => il.User);
            
            return MapToInventoryLogDto(createdLog!);
        }

        public async Task<IEnumerable<InventoryLogDto>> GetMovementsByProductAsync(int productId)
        {
            var movements = await _unitOfWork.Inventory.GetByProductAsync(productId);
            return movements.Select(MapToInventoryLogDto);
        }

        public async Task<IEnumerable<InventoryLogDto>> GetMovementsByTypeAsync(string movementType)
        {
            var movements = await _unitOfWork.Inventory.GetByMovementTypeAsync(movementType);
            return movements.Select(MapToInventoryLogDto);
        }

        public async Task<IEnumerable<InventoryLogDto>> GetMovementsByUserAsync(int userId)
        {
            var movements = await _unitOfWork.Inventory.GetByUserAsync(userId);
            return movements.Select(MapToInventoryLogDto);
        }

        public async Task<IEnumerable<InventoryLogDto>> GetMovementsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var movements = await _unitOfWork.Inventory.GetByDateRangeAsync(fromDate, toDate);
            return movements.Select(MapToInventoryLogDto);
        }

        public async Task<IEnumerable<InventoryLogDto>> GetRecentMovementsAsync(int count = 50)
        {
            var movements = await _unitOfWork.Inventory.GetRecentMovementsAsync(count);
            return movements.Select(MapToInventoryLogDto);
        }

        public async Task<int> GetCurrentStockAsync(int productId)
        {
            return await _unitOfWork.Inventory.GetCurrentStockAsync(productId);
        }

        public async Task<Dictionary<int, int>> GetCurrentStockForProductsAsync(IEnumerable<int> productIds)
        {
            return await _unitOfWork.Inventory.GetCurrentStockForProductsAsync(productIds);
        }

        public async Task<IEnumerable<ProductStockDto>> GetStockSummaryAsync()
        {
            var stockSummary = await _unitOfWork.Inventory.GetStockSummaryAsync();
            return stockSummary.Cast<dynamic>().Select(item => new ProductStockDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                CurrentQuantity = item.CurrentStock,
                LastMovementDate = item.LastMovement,
                Status = GetStockStatus(item.CurrentStock)
            });
        }

        public async Task<bool> AdjustStockAsync(StockAdjustmentDto adjustmentDto)
        {
            // Validation
            if (!await _unitOfWork.Products.AnyAsync(p => p.Id == adjustmentDto.ProductId))
                return false;

            if (!await _unitOfWork.Users.AnyAsync(u => u.Id == adjustmentDto.UserId))
                throw new InvalidOperationException($"User with ID {adjustmentDto.UserId} not found.");

            // Check if adjustment would result in negative stock
            if (adjustmentDto.AdjustmentQuantity < 0)
            {
                var currentStock = await GetCurrentStockAsync(adjustmentDto.ProductId);
                if (currentStock + adjustmentDto.AdjustmentQuantity < 0)
                    throw new InvalidOperationException("Adjustment would result in negative stock.");
            }

            var product = await _unitOfWork.Products.GetByIdAsync(adjustmentDto.ProductId);
            
            var inventoryLog = new InventoryLog
            {
                ProductId = adjustmentDto.ProductId,
                MovementType = "adjust",
                Quantity = adjustmentDto.AdjustmentQuantity,
                UnitCost = product!.DefaultCostPrice,
                ReferenceTable = "stock_adjustment",
                ReferenceId = 0,
                Note = adjustmentDto.Reason,
                UserId = adjustmentDto.UserId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.Inventory.AddAsync(inventoryLog);
            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<IEnumerable<LowStockAlertDto>> GetLowStockAlertsAsync(int threshold = 10)
        {
            var lowStockProducts = await _unitOfWork.Inventory.GetLowStockAlertsAsync(threshold);
            return lowStockProducts.Cast<dynamic>().Select(item => new LowStockAlertDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                CategoryName = item.CategoryName,
                CurrentStock = item.CurrentStock,
                LastMovement = item.LastMovement,
                AlertLevel = GetAlertLevel(item.CurrentStock, threshold)
            });
        }

        public async Task<IEnumerable<LowStockAlertDto>> GetOutOfStockProductsAsync()
        {
            var outOfStockProducts = await _unitOfWork.Inventory.GetOutOfStockProductsAsync();
            return outOfStockProducts.Cast<dynamic>().Select(item => new LowStockAlertDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                CategoryName = item.CategoryName,
                CurrentStock = item.CurrentStock,
                LastMovement = item.LastMovement,
                AlertLevel = "OutOfStock"
            });
        }

        public async Task<IEnumerable<LowStockAlertDto>> GetCriticalStockAlertsAsync(int criticalThreshold = 5)
        {
            var criticalStockProducts = await _unitOfWork.Inventory.GetLowStockAlertsAsync(criticalThreshold);
            return criticalStockProducts.Cast<dynamic>()
                .Where(item => item.CurrentStock <= criticalThreshold && item.CurrentStock > 0)
                .Select(item => new LowStockAlertDto
                {
                    ProductId = item.ProductId,
                    ProductName = item.ProductName,
                    CategoryName = item.CategoryName,
                    CurrentStock = item.CurrentStock,
                    LastMovement = item.LastMovement,
                    AlertLevel = "Critical"
                });
        }

        public async Task<decimal> GetInventoryValueAsync(int? productId = null)
        {
            return await _unitOfWork.Inventory.GetInventoryValueAsync(productId);
        }

        public async Task<IEnumerable<InventoryValuationDto>> GetInventoryValuationReportAsync()
        {
            var valuationReport = await _unitOfWork.Inventory.GetInventoryValuationReportAsync();
            return valuationReport.Cast<dynamic>().Select(item => new InventoryValuationDto
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                CurrentStock = item.CurrentStock,
                AverageCost = item.AverageCost,
                TotalValue = item.TotalValue
            });
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            return await GetInventoryValueAsync();
        }

        public async Task<IEnumerable<InventoryMovementDto>> GetMovementsByTypeReportAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var movementReport = await _unitOfWork.Inventory.GetMovementsByTypeAsync(fromDate, toDate);
            return movementReport.Cast<dynamic>().Select(item => new InventoryMovementDto
            {
                MovementType = item.MovementType,
                Count = item.Count,
                TotalQuantity = item.TotalQuantity,
                TotalValue = item.TotalValue
            });
        }

        public async Task<IEnumerable<DailyMovementDto>> GetDailyMovementsReportAsync(DateTime fromDate, DateTime toDate)
        {
            var dailyReport = await _unitOfWork.Inventory.GetDailyMovementsAsync(fromDate, toDate);
            return dailyReport.Cast<dynamic>().Select(item => new DailyMovementDto
            {
                Date = item.Date,
                TotalMovements = item.TotalMovements,
                TotalIn = item.TotalIn,
                TotalOut = item.TotalOut,
                TotalValue = item.TotalValue
            });
        }

        private InventoryLogDto MapToInventoryLogDto(InventoryLog inventoryLog)
        {
            return new InventoryLogDto
            {
                Id = inventoryLog.Id,
                ProductId = inventoryLog.ProductId,
                ProductName = inventoryLog.Product?.Name ?? "",
                MovementType = inventoryLog.MovementType,
                Quantity = inventoryLog.Quantity,
                UnitCost = inventoryLog.UnitCost,
                ReferenceTable = inventoryLog.ReferenceTable,
                ReferenceId = inventoryLog.ReferenceId,
                Note = inventoryLog.Note,
                UserId = inventoryLog.UserId,
                UserName = inventoryLog.User?.Username ?? "",
                CreatedAt = inventoryLog.CreatedAt
            };
        }

        private string GetStockStatus(int currentStock)
        {
            if (currentStock <= 0)
                return "OutOfStock";
            else if (currentStock <= 5)
                return "Critical";
            else if (currentStock <= 10)
                return "Low";
            else
                return "InStock";
        }

        private string GetAlertLevel(int currentStock, int threshold)
        {
            if (currentStock <= 0)
                return "OutOfStock";
            else if (currentStock <= threshold / 2)
                return "Critical";
            else
                return "Low";
        }

        public async Task<object> GetInventoryStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var totalValue = await GetTotalInventoryValueAsync();
            var lowStockCount = (await GetLowStockAlertsAsync()).Count();
            var outOfStockCount = (await GetOutOfStockProductsAsync()).Count();
            var criticalStockCount = (await GetCriticalStockAlertsAsync()).Count();

            var movementStats = await GetMovementsByTypeReportAsync(fromDate, toDate);
            var totalMovements = movementStats.Sum(m => m.Count);

            return new
            {
                TotalInventoryValue = totalValue,
                LowStockProducts = lowStockCount,
                OutOfStockProducts = outOfStockCount,
                CriticalStockProducts = criticalStockCount,
                TotalMovements = totalMovements,
                MovementsByType = movementStats
            };
        }

        public async Task<bool> TransferStockAsync(StockTransferDto transferDto)
        {
            // Validation
            if (transferDto.FromProductId == transferDto.ToProductId)
                throw new InvalidOperationException("Cannot transfer stock to the same product.");

            if (!await _unitOfWork.Products.AnyAsync(p => p.Id == transferDto.FromProductId))
                throw new InvalidOperationException($"Source product with ID {transferDto.FromProductId} not found.");

            if (!await _unitOfWork.Products.AnyAsync(p => p.Id == transferDto.ToProductId))
                throw new InvalidOperationException($"Destination product with ID {transferDto.ToProductId} not found.");

            if (!await HasSufficientStockAsync(transferDto.FromProductId, transferDto.Quantity))
                throw new InvalidOperationException("Insufficient stock for transfer.");

            await _unitOfWork.BeginTransactionAsync();
            try
            {
                var fromProduct = await _unitOfWork.Products.GetByIdAsync(transferDto.FromProductId);
                var toProduct = await _unitOfWork.Products.GetByIdAsync(transferDto.ToProductId);

                // Create outgoing movement for source product
                var outgoingLog = new InventoryLog
                {
                    ProductId = transferDto.FromProductId,
                    MovementType = "transfer_out",
                    Quantity = -transferDto.Quantity,
                    UnitCost = fromProduct!.DefaultCostPrice,
                    ReferenceTable = "stock_transfer",
                    ReferenceId = transferDto.ToProductId,
                    Note = $"Transfer to {toProduct!.Name}: {transferDto.Reason}",
                    UserId = transferDto.UserId,
                    CreatedAt = DateTime.Now
                };

                // Create incoming movement for destination product
                var incomingLog = new InventoryLog
                {
                    ProductId = transferDto.ToProductId,
                    MovementType = "transfer_in",
                    Quantity = transferDto.Quantity,
                    UnitCost = toProduct.DefaultCostPrice,
                    ReferenceTable = "stock_transfer",
                    ReferenceId = transferDto.FromProductId,
                    Note = $"Transfer from {fromProduct.Name}: {transferDto.Reason}",
                    UserId = transferDto.UserId,
                    CreatedAt = DateTime.Now
                };

                await _unitOfWork.Inventory.AddAsync(outgoingLog);
                await _unitOfWork.Inventory.AddAsync(incomingLog);
                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return true;
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        public async Task<bool> BulkAdjustStockAsync(IEnumerable<StockAdjustmentDto> adjustments)
        {
            await _unitOfWork.BeginTransactionAsync();
            try
            {
                foreach (var adjustment in adjustments)
                {
                    await AdjustStockAsync(adjustment);
                }

                await _unitOfWork.CommitTransactionAsync();
                return true;
            }
            catch
            {
                await _unitOfWork.RollbackTransactionAsync();
                throw;
            }
        }

        public async Task<IEnumerable<InventoryLogDto>> SearchMovementsAsync(InventorySearchDto searchDto)
        {
            var movements = await _unitOfWork.Inventory.GetMovementsWithDetailsAsync(
                searchDto.ProductId, searchDto.MovementType, searchDto.FromDate, searchDto.ToDate);

            var filteredMovements = movements.AsQueryable();

            if (searchDto.UserId.HasValue)
                filteredMovements = filteredMovements.Where(m => m.UserId == searchDto.UserId.Value);

            if (!string.IsNullOrEmpty(searchDto.SearchTerm))
            {
                filteredMovements = filteredMovements.Where(m =>
                    m.Product.Name.Contains(searchDto.SearchTerm) ||
                    (m.Note != null && m.Note.Contains(searchDto.SearchTerm)));
            }

            return filteredMovements.Select(MapToInventoryLogDto);
        }

        public async Task<bool> CanAdjustStockAsync(int productId, int quantity)
        {
            if (quantity >= 0)
                return true;

            var currentStock = await GetCurrentStockAsync(productId);
            return currentStock + quantity >= 0;
        }

        public async Task<bool> HasSufficientStockAsync(int productId, int requiredQuantity)
        {
            var currentStock = await GetCurrentStockAsync(productId);
            return currentStock >= requiredQuantity;
        }

        public async Task<(IEnumerable<InventoryLogDto> Movements, int TotalCount)> GetPagedMovementsAsync(
            int pageNumber, int pageSize, InventorySearchDto? searchDto = null)
        {
            var (movements, totalCount) = await _unitOfWork.Inventory.GetPagedMovementsAsync(
                pageNumber, pageSize, searchDto?.ProductId, searchDto?.MovementType,
                searchDto?.FromDate, searchDto?.ToDate);

            var movementDtos = movements.Select(MapToInventoryLogDto);
            return (movementDtos, totalCount);
        }

        public async Task<decimal> CalculateAverageCostAsync(int productId)
        {
            var movements = await _unitOfWork.Inventory.GetByProductAsync(productId);
            var positiveMovements = movements.Where(m => m.Quantity > 0);

            if (!positiveMovements.Any())
                return 0;

            var totalCost = positiveMovements.Sum(m => m.Quantity * m.UnitCost);
            var totalQuantity = positiveMovements.Sum(m => m.Quantity);

            return totalQuantity > 0 ? totalCost / totalQuantity : 0;
        }

        public async Task<decimal> CalculateFIFOCostAsync(int productId, int quantity)
        {
            var movements = await _unitOfWork.Inventory.GetByProductAsync(productId);
            var positiveMovements = movements
                .Where(m => m.Quantity > 0)
                .OrderBy(m => m.CreatedAt)
                .ToList();

            decimal totalCost = 0;
            int remainingQuantity = quantity;

            foreach (var movement in positiveMovements)
            {
                if (remainingQuantity <= 0)
                    break;

                int quantityToUse = Math.Min(remainingQuantity, movement.Quantity);
                totalCost += quantityToUse * movement.UnitCost;
                remainingQuantity -= quantityToUse;
            }

            return totalCost;
        }

        public async Task<IEnumerable<object>> GetCostAnalysisAsync(int productId)
        {
            var movements = await _unitOfWork.Inventory.GetByProductAsync(productId);
            var positiveMovements = movements.Where(m => m.Quantity > 0);

            var averageCost = await CalculateAverageCostAsync(productId);
            var currentStock = await GetCurrentStockAsync(productId);
            var fifoCost = currentStock > 0 ? await CalculateFIFOCostAsync(productId, currentStock) : 0;

            return new[]
            {
                new
                {
                    Method = "Average Cost",
                    UnitCost = averageCost,
                    TotalValue = averageCost * currentStock
                },
                new
                {
                    Method = "FIFO",
                    UnitCost = currentStock > 0 ? fifoCost / currentStock : 0,
                    TotalValue = fifoCost
                }
            };
        }

        public async Task<IEnumerable<InventoryLogDto>> GetAuditTrailAsync(int productId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var movements = await _unitOfWork.Inventory.GetMovementsWithDetailsAsync(
                productId, null, fromDate, toDate);

            return movements.Select(MapToInventoryLogDto);
        }

        public async Task<object> GetMovementSummaryAsync(int productId)
        {
            var movements = await _unitOfWork.Inventory.GetByProductAsync(productId);
            var currentStock = await GetCurrentStockAsync(productId);

            var summary = movements.GroupBy(m => m.MovementType)
                .Select(g => new
                {
                    MovementType = g.Key,
                    Count = g.Count(),
                    TotalQuantity = g.Sum(m => Math.Abs(m.Quantity)),
                    TotalValue = g.Sum(m => Math.Abs(m.Quantity) * m.UnitCost),
                    LastMovement = g.Max(m => m.CreatedAt)
                });

            return new
            {
                ProductId = productId,
                CurrentStock = currentStock,
                TotalMovements = movements.Count(),
                MovementsByType = summary,
                FirstMovement = movements.Any() ? movements.Min(m => m.CreatedAt) : (DateTime?)null,
                LastMovement = movements.Any() ? movements.Max(m => m.CreatedAt) : (DateTime?)null
            };
        }
    }
}

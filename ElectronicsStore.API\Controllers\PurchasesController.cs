using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PurchasesController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public PurchasesController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get all purchase invoices
        /// </summary>
        /// <returns>List of purchase invoices</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoices()
        {
            var purchases = await _unitOfWork.PurchaseInvoices.GetAllAsync();
            var purchaseDtos = purchases.Select(p => new PurchaseInvoiceDto
            {
                Id = p.Id,
                InvoiceNumber = p.InvoiceNumber,
                SupplierId = p.SupplierId,
                SupplierName = p.Supplier?.Name ?? "",
                InvoiceDate = p.InvoiceDate,
                UserId = p.UserId,
                UserName = p.User?.Username ?? "",
                TotalAmount = p.TotalAmount,
                Details = p.PurchaseInvoiceDetails?.Select(d => new PurchaseInvoiceDetailDto
                {
                    Id = d.Id,
                    PurchaseInvoiceId = d.PurchaseInvoiceId,
                    ProductId = d.ProductId,
                    ProductName = d.Product?.Name ?? "",
                    Quantity = d.Quantity,
                    UnitCost = d.UnitCost,
                    LineTotal = d.LineTotal
                }).ToList() ?? new List<PurchaseInvoiceDetailDto>()
            });

            return Ok(purchaseDtos);
        }

        /// <summary>
        /// Get purchase invoice by ID
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <returns>Purchase invoice details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> GetPurchaseInvoice(int id)
        {
            var purchase = await _unitOfWork.PurchaseInvoices.GetByIdAsync(id);
            if (purchase == null)
                return NotFound($"Purchase invoice with ID {id} not found.");

            var purchaseDto = new PurchaseInvoiceDto
            {
                Id = purchase.Id,
                InvoiceNumber = purchase.InvoiceNumber,
                SupplierId = purchase.SupplierId,
                SupplierName = purchase.Supplier?.Name ?? "",
                InvoiceDate = purchase.InvoiceDate,
                UserId = purchase.UserId,
                UserName = purchase.User?.Username ?? "",
                TotalAmount = purchase.TotalAmount,
                Details = purchase.PurchaseInvoiceDetails?.Select(d => new PurchaseInvoiceDetailDto
                {
                    Id = d.Id,
                    PurchaseInvoiceId = d.PurchaseInvoiceId,
                    ProductId = d.ProductId,
                    ProductName = d.Product?.Name ?? "",
                    Quantity = d.Quantity,
                    UnitCost = d.UnitCost,
                    LineTotal = d.LineTotal
                }).ToList() ?? new List<PurchaseInvoiceDetailDto>()
            };

            return Ok(purchaseDto);
        }

        /// <summary>
        /// Create a new purchase invoice
        /// </summary>
        /// <param name="createPurchaseDto">Purchase invoice creation data</param>
        /// <returns>Created purchase invoice</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> CreatePurchaseInvoice(CreatePurchaseInvoiceDto createPurchaseDto)
        {
            try
            {
                // Validation
                if (await _unitOfWork.PurchaseInvoices.AnyAsync(p => p.InvoiceNumber == createPurchaseDto.InvoiceNumber))
                    return BadRequest($"Invoice number '{createPurchaseDto.InvoiceNumber}' already exists.");

                if (!await _unitOfWork.Suppliers.AnyAsync(s => s.Id == createPurchaseDto.SupplierId))
                    return BadRequest($"Supplier with ID {createPurchaseDto.SupplierId} not found.");

                if (!await _unitOfWork.Users.AnyAsync(u => u.Id == createPurchaseDto.UserId))
                    return BadRequest($"User with ID {createPurchaseDto.UserId} not found.");

                if (!createPurchaseDto.Details.Any())
                    return BadRequest("Purchase invoice must have at least one detail item.");

                await _unitOfWork.BeginTransactionAsync();

                // Create purchase invoice
                var purchase = new Domain.Entities.PurchaseInvoice
                {
                    InvoiceNumber = createPurchaseDto.InvoiceNumber,
                    SupplierId = createPurchaseDto.SupplierId,
                    InvoiceDate = DateTime.Now,
                    UserId = createPurchaseDto.UserId,
                    TotalAmount = 0
                };

                await _unitOfWork.PurchaseInvoices.AddAsync(purchase);
                await _unitOfWork.SaveChangesAsync();

                decimal totalAmount = 0;

                // Create purchase invoice details and update inventory
                foreach (var detailDto in createPurchaseDto.Details)
                {
                    var product = await _unitOfWork.Products.GetByIdAsync(detailDto.ProductId);
                    if (product == null)
                        return BadRequest($"Product with ID {detailDto.ProductId} not found.");

                    var detail = new Domain.Entities.PurchaseInvoiceDetail
                    {
                        PurchaseInvoiceId = purchase.Id,
                        ProductId = detailDto.ProductId,
                        Quantity = detailDto.Quantity,
                        UnitCost = detailDto.UnitCost
                    };

                    await _unitOfWork.PurchaseInvoiceDetails.AddAsync(detail);

                    var lineTotal = detailDto.UnitCost * detailDto.Quantity;
                    totalAmount += lineTotal;

                    // Create inventory movement
                    var inventoryLog = new Domain.Entities.InventoryLog
                    {
                        ProductId = detailDto.ProductId,
                        MovementType = "purchase",
                        Quantity = detailDto.Quantity,
                        UnitCost = detailDto.UnitCost,
                        ReferenceTable = "purchase_invoices",
                        ReferenceId = purchase.Id,
                        Note = $"Purchase - Invoice #{purchase.InvoiceNumber}",
                        UserId = createPurchaseDto.UserId,
                        CreatedAt = DateTime.Now
                    };

                    await _unitOfWork.Inventory.AddAsync(inventoryLog);
                }

                // Update total amount
                purchase.TotalAmount = totalAmount;
                _unitOfWork.PurchaseInvoices.Update(purchase);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return CreatedAtAction(nameof(GetPurchaseInvoice), new { id = purchase.Id }, 
                    await GetPurchaseInvoice(purchase.Id));
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest($"Error creating purchase invoice: {ex.Message}");
            }
        }

        /// <summary>
        /// Delete a purchase invoice
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeletePurchaseInvoice(int id)
        {
            try
            {
                var purchase = await _unitOfWork.PurchaseInvoices.GetByIdAsync(id);
                if (purchase == null)
                    return NotFound($"Purchase invoice with ID {id} not found.");

                await _unitOfWork.BeginTransactionAsync();

                // Reverse inventory movements
                var inventoryLogs = await _unitOfWork.Inventory.GetAllAsync();
                var relatedLogs = inventoryLogs.Where(il => il.ReferenceTable == "purchase_invoices" && il.ReferenceId == id);

                foreach (var log in relatedLogs)
                {
                    var reversalLog = new Domain.Entities.InventoryLog
                    {
                        ProductId = log.ProductId,
                        MovementType = "purchase_reversal",
                        Quantity = -log.Quantity,
                        UnitCost = log.UnitCost,
                        ReferenceTable = "purchase_invoices",
                        ReferenceId = id,
                        Note = $"Purchase reversal - Invoice #{purchase.InvoiceNumber} deleted",
                        UserId = purchase.UserId,
                        CreatedAt = DateTime.Now
                    };

                    await _unitOfWork.Inventory.AddAsync(reversalLog);
                }

                // Delete purchase invoice details
                var details = await _unitOfWork.PurchaseInvoiceDetails.GetAllAsync();
                var purchaseDetails = details.Where(d => d.PurchaseInvoiceId == id);
                _unitOfWork.PurchaseInvoiceDetails.RemoveRange(purchaseDetails);

                // Delete purchase invoice
                _unitOfWork.PurchaseInvoices.Remove(purchase);

                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                return NoContent();
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return BadRequest($"Error deleting purchase invoice: {ex.Message}");
            }
        }

        /// <summary>
        /// Search purchase invoices
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching purchase invoices</returns>
        [HttpPost("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> SearchPurchaseInvoices(PurchaseSearchDto searchDto)
        {
            var purchases = await _unitOfWork.PurchaseInvoices.GetAllAsync();
            var filteredPurchases = purchases.AsQueryable();

            if (!string.IsNullOrEmpty(searchDto.SearchTerm))
                filteredPurchases = filteredPurchases.Where(p => p.InvoiceNumber.Contains(searchDto.SearchTerm));

            if (searchDto.SupplierId.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.SupplierId == searchDto.SupplierId.Value);

            if (searchDto.UserId.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.UserId == searchDto.UserId.Value);

            if (searchDto.FromDate.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.InvoiceDate >= searchDto.FromDate.Value);

            if (searchDto.ToDate.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.InvoiceDate <= searchDto.ToDate.Value);

            if (searchDto.MinAmount.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.TotalAmount >= searchDto.MinAmount.Value);

            if (searchDto.MaxAmount.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.TotalAmount <= searchDto.MaxAmount.Value);

            var purchaseDtos = filteredPurchases.Select(p => new PurchaseInvoiceDto
            {
                Id = p.Id,
                InvoiceNumber = p.InvoiceNumber,
                SupplierId = p.SupplierId,
                SupplierName = p.Supplier?.Name ?? "",
                InvoiceDate = p.InvoiceDate,
                UserId = p.UserId,
                UserName = p.User?.Username ?? "",
                TotalAmount = p.TotalAmount
            });

            return Ok(purchaseDtos);
        }

        /// <summary>
        /// Get purchase statistics
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Purchase statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPurchaseStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var purchases = await _unitOfWork.PurchaseInvoices.GetAllAsync();
            var filteredPurchases = purchases.AsQueryable();

            if (fromDate.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                filteredPurchases = filteredPurchases.Where(p => p.InvoiceDate <= toDate.Value);

            var statistics = new
            {
                TotalPurchases = filteredPurchases.Count(),
                TotalAmount = filteredPurchases.Sum(p => p.TotalAmount),
                AverageAmount = filteredPurchases.Any() ? filteredPurchases.Average(p => p.TotalAmount) : 0,
                TotalItems = filteredPurchases.SelectMany(p => p.PurchaseInvoiceDetails ?? new List<Domain.Entities.PurchaseInvoiceDetail>()).Sum(d => d.Quantity)
            };

            return Ok(statistics);
        }
    }
}

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using AutoMapper;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PurchasesController : ControllerBase
    {
        private readonly IPurchaseService _purchaseService;
        private readonly IMapper _mapper;

        public PurchasesController(IPurchaseService purchaseService, IMapper mapper)
        {
            _purchaseService = purchaseService;
            _mapper = mapper;
        }

        /// <summary>
        /// Get all purchase invoices
        /// </summary>
        /// <returns>List of purchase invoices</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoices()
        {
            var purchaseInvoices = await _purchaseService.GetAllPurchaseInvoicesAsync();
            return Ok(purchaseInvoices);
        }

        /// <summary>
        /// Get purchase invoice by ID
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <returns>Purchase invoice details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> GetPurchaseInvoice(int id)
        {
            var purchaseInvoice = await _purchaseService.GetPurchaseInvoiceByIdAsync(id);
            if (purchaseInvoice == null)
                return NotFound($"Purchase invoice with ID {id} not found.");

            return Ok(purchaseInvoice);
        }

        /// <summary>
        /// Get purchase invoice by invoice number
        /// </summary>
        /// <param name="invoiceNumber">Invoice number</param>
        /// <returns>Purchase invoice details</returns>
        [HttpGet("number/{invoiceNumber}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> GetPurchaseInvoiceByNumber(string invoiceNumber)
        {
            var purchaseInvoice = await _purchaseService.GetPurchaseInvoiceByNumberAsync(invoiceNumber);
            if (purchaseInvoice == null)
                return NotFound($"Purchase invoice with number '{invoiceNumber}' not found.");

            return Ok(purchaseInvoice);
        }

        /// <summary>
        /// Create a new purchase invoice
        /// </summary>
        /// <param name="createPurchaseInvoiceDto">Purchase invoice creation data</param>
        /// <returns>Created purchase invoice</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseInvoiceDto>> CreatePurchaseInvoice(CreatePurchaseInvoiceDto createPurchaseInvoiceDto)
        {
            try
            {
                var purchaseInvoice = await _purchaseService.CreatePurchaseInvoiceAsync(createPurchaseInvoiceDto);
                return CreatedAtAction(nameof(GetPurchaseInvoice), new { id = purchaseInvoice.Id }, purchaseInvoice);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Delete a purchase invoice
        /// </summary>
        /// <param name="id">Purchase invoice ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeletePurchaseInvoice(int id)
        {
            try
            {
                var result = await _purchaseService.DeletePurchaseInvoiceAsync(id);
                if (!result)
                    return NotFound($"Purchase invoice with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Search purchase invoices
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching purchase invoices</returns>
        [HttpPost("search")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> SearchPurchaseInvoices(PurchaseSearchDto searchDto)
        {
            var purchaseInvoices = await _purchaseService.SearchPurchaseInvoicesAsync(searchDto);
            return Ok(purchaseInvoices);
        }

        /// <summary>
        /// Get purchase invoices by supplier
        /// </summary>
        /// <param name="supplierId">Supplier ID</param>
        /// <returns>List of supplier's purchase invoices</returns>
        [HttpGet("supplier/{supplierId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoicesBySupplier(int supplierId)
        {
            var purchaseInvoices = await _purchaseService.GetPurchaseInvoicesBySupplierAsync(supplierId);
            return Ok(purchaseInvoices);
        }

        /// <summary>
        /// Get purchase invoices by user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of user's purchase invoices</returns>
        [HttpGet("user/{userId}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetPurchaseInvoicesByUser(int userId)
        {
            var purchaseInvoices = await _purchaseService.GetPurchaseInvoicesByUserAsync(userId);
            return Ok(purchaseInvoices);
        }

        /// <summary>
        /// Get today's purchases
        /// </summary>
        /// <returns>List of today's purchase invoices</returns>
        [HttpGet("today")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseInvoiceDto>>> GetTodaysPurchases()
        {
            var purchaseInvoices = await _purchaseService.GetTodaysPurchasesAsync();
            return Ok(purchaseInvoices);
        }

        /// <summary>
        /// Get purchase statistics
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Purchase statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPurchaseStatistics([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var statistics = await _purchaseService.GetPurchaseStatisticsAsync(fromDate, toDate);
            return Ok(statistics);
        }

        /// <summary>
        /// Get daily purchase report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Daily purchase report</returns>
        [HttpGet("reports/daily")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetDailyPurchaseReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _purchaseService.GetDailyPurchaseReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get monthly purchase report
        /// </summary>
        /// <param name="year">Year</param>
        /// <returns>Monthly purchase report</returns>
        [HttpGet("reports/monthly/{year}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetMonthlyPurchaseReport(int year)
        {
            var report = await _purchaseService.GetMonthlyPurchaseReportAsync(year);
            return Ok(report);
        }

        /// <summary>
        /// Get purchases by supplier
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Purchases breakdown by supplier</returns>
        [HttpGet("reports/suppliers")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetPurchasesBySupplier([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var report = await _purchaseService.GetPurchasesBySupplierAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get top suppliers
        /// </summary>
        /// <param name="count">Number of suppliers to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of top suppliers</returns>
        [HttpGet("reports/top-suppliers")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetTopSuppliers(
            [FromQuery] int count = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var topSuppliers = await _purchaseService.GetTopSuppliersAsync(count, fromDate, toDate);
            return Ok(topSuppliers);
        }

        /// <summary>
        /// Get top purchased products
        /// </summary>
        /// <param name="count">Number of products to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of top purchased products</returns>
        [HttpGet("reports/top-products")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<object>>> GetTopPurchasedProducts(
            [FromQuery] int count = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var topProducts = await _purchaseService.GetTopPurchasedProductsAsync(count, fromDate, toDate);
            return Ok(topProducts);
        }

        /// <summary>
        /// Process a purchase return
        /// </summary>
        /// <param name="purchaseInvoiceId">Purchase invoice ID</param>
        /// <param name="request">Return request data</param>
        /// <returns>Success status</returns>
        [HttpPost("{purchaseInvoiceId}/return")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> ProcessReturn(int purchaseInvoiceId, [FromBody] object request)
        {
            try
            {
                var currentUserIdClaim = User.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(currentUserIdClaim) || !int.TryParse(currentUserIdClaim, out int currentUserId))
                    return Unauthorized("Invalid token.");

                // Parse request body
                var requestData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(request.ToString()!);
                var productId = Convert.ToInt32(requestData!["productId"]);
                var quantity = Convert.ToInt32(requestData["quantity"]);
                var reason = requestData["reason"].ToString() ?? "";

                var result = await _purchaseService.ProcessPurchaseReturnAsync(purchaseInvoiceId, productId, quantity, reason, currentUserId);
                if (!result)
                    return NotFound($"Purchase invoice with ID {purchaseInvoiceId} not found.");

                return Ok(new { Message = "Return processed successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get purchase returns
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of purchase returns</returns>
        [HttpGet("returns")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<PurchaseReturnDto>>> GetPurchaseReturns([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var returns = await _purchaseService.GetPurchaseReturnsAsync(fromDate, toDate);
            return Ok(returns);
        }

        /// <summary>
        /// Get paged purchase invoices
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of purchase invoices</returns>
        [HttpGet("paged")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPagedPurchaseInvoices(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] PurchaseSearchDto? searchDto = null)
        {
            var (purchases, totalCount) = await _purchaseService.GetPagedPurchaseInvoicesAsync(pageNumber, pageSize, searchDto);
            
            return Ok(new
            {
                Purchases = purchases,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
    }
}

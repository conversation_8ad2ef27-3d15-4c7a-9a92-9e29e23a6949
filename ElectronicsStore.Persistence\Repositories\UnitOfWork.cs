using Microsoft.EntityFrameworkCore.Storage;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Persistence.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ElectronicsDbContext _context;
        private IDbContextTransaction? _transaction;

        // Repository instances
        private IGenericRepository<Category>? _categories;
        private IGenericRepository<Supplier>? _suppliers;
        private IGenericRepository<Role>? _roles;
        private IGenericRepository<Permission>? _permissions;
        private IGenericRepository<RolePermission>? _rolePermissions;
        private IGenericRepository<User>? _users;
        private IGenericRepository<Product>? _products;
        private IGenericRepository<PurchaseInvoice>? _purchaseInvoices;
        private IGenericRepository<PurchaseInvoiceDetail>? _purchaseInvoiceDetails;
        private IGenericRepository<SalesInvoice>? _salesInvoices;
        private IGenericRepository<SalesInvoiceDetail>? _salesInvoiceDetails;
        private IGenericRepository<InventoryLog>? _inventoryLogs;
        private IGenericRepository<SalesReturn>? _salesReturns;
        private IGenericRepository<PurchaseReturn>? _purchaseReturns;
        private IGenericRepository<Expense>? _expenses;
        private IGenericRepository<InventoryView>? _inventoryViews;
        private IGenericRepository<InventoryValuationView>? _inventoryValuationViews;
        private IGenericRepository<CogsView>? _cogsViews;

        public UnitOfWork(ElectronicsDbContext context)
        {
            _context = context;
        }

        // Repository properties with lazy initialization
        public IGenericRepository<Category> Categories => 
            _categories ??= new GenericRepository<Category>(_context);

        public IGenericRepository<Supplier> Suppliers => 
            _suppliers ??= new GenericRepository<Supplier>(_context);

        public IGenericRepository<Role> Roles => 
            _roles ??= new GenericRepository<Role>(_context);

        public IGenericRepository<Permission> Permissions => 
            _permissions ??= new GenericRepository<Permission>(_context);

        public IGenericRepository<RolePermission> RolePermissions => 
            _rolePermissions ??= new GenericRepository<RolePermission>(_context);

        public IGenericRepository<User> Users => 
            _users ??= new GenericRepository<User>(_context);

        public IGenericRepository<Product> Products => 
            _products ??= new GenericRepository<Product>(_context);

        public IGenericRepository<PurchaseInvoice> PurchaseInvoices => 
            _purchaseInvoices ??= new GenericRepository<PurchaseInvoice>(_context);

        public IGenericRepository<PurchaseInvoiceDetail> PurchaseInvoiceDetails => 
            _purchaseInvoiceDetails ??= new GenericRepository<PurchaseInvoiceDetail>(_context);

        public IGenericRepository<SalesInvoice> SalesInvoices => 
            _salesInvoices ??= new GenericRepository<SalesInvoice>(_context);

        public IGenericRepository<SalesInvoiceDetail> SalesInvoiceDetails => 
            _salesInvoiceDetails ??= new GenericRepository<SalesInvoiceDetail>(_context);

        public IGenericRepository<InventoryLog> InventoryLogs => 
            _inventoryLogs ??= new GenericRepository<InventoryLog>(_context);

        public IGenericRepository<SalesReturn> SalesReturns => 
            _salesReturns ??= new GenericRepository<SalesReturn>(_context);

        public IGenericRepository<PurchaseReturn> PurchaseReturns => 
            _purchaseReturns ??= new GenericRepository<PurchaseReturn>(_context);

        public IGenericRepository<Expense> Expenses => 
            _expenses ??= new GenericRepository<Expense>(_context);

        public IGenericRepository<InventoryView> InventoryViews => 
            _inventoryViews ??= new GenericRepository<InventoryView>(_context);

        public IGenericRepository<InventoryValuationView> InventoryValuationViews => 
            _inventoryValuationViews ??= new GenericRepository<InventoryValuationView>(_context);

        public IGenericRepository<CogsView> CogsViews => 
            _cogsViews ??= new GenericRepository<CogsView>(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Persistence.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ElectronicsDbContext _context;
        private IDbContextTransaction? _transaction;

        // Generic repository instances
        private IGenericRepository<Category>? _categories;
        private IGenericRepository<Supplier>? _suppliers;
        private IGenericRepository<Role>? _roles;
        private IGenericRepository<Permission>? _permissions;
        private IGenericRepository<RolePermission>? _rolePermissions;
        private IGenericRepository<PurchaseInvoiceDetail>? _purchaseInvoiceDetails;
        private IGenericRepository<SalesInvoiceDetail>? _salesInvoiceDetails;
        private IGenericRepository<SalesReturn>? _salesReturns;
        private IGenericRepository<PurchaseReturn>? _purchaseReturns;
        private IGenericRepository<Expense>? _expenses;
        private IGenericRepository<InventoryView>? _inventoryViews;
        private IGenericRepository<InventoryValuationView>? _inventoryValuationViews;
        private IGenericRepository<CogsView>? _cogsViews;

        // Specialized repository instances
        private IUserRepository? _users;
        private IProductRepository? _products;
        private ISalesRepository? _salesInvoices;
        private IPurchaseRepository? _purchaseInvoices;
        private IInventoryRepository? _inventoryLogs;

        public UnitOfWork(ElectronicsDbContext context)
        {
            _context = context;
        }

        // Generic repository properties with lazy initialization
        public IGenericRepository<Category> Categories =>
            _categories ??= new GenericRepository<Category>(_context);

        public IGenericRepository<Supplier> Suppliers =>
            _suppliers ??= new GenericRepository<Supplier>(_context);

        public IGenericRepository<Role> Roles =>
            _roles ??= new GenericRepository<Role>(_context);

        public IGenericRepository<Permission> Permissions =>
            _permissions ??= new GenericRepository<Permission>(_context);

        public IGenericRepository<RolePermission> RolePermissions =>
            _rolePermissions ??= new GenericRepository<RolePermission>(_context);

        public IGenericRepository<PurchaseInvoiceDetail> PurchaseInvoiceDetails =>
            _purchaseInvoiceDetails ??= new GenericRepository<PurchaseInvoiceDetail>(_context);

        public IGenericRepository<SalesInvoiceDetail> SalesInvoiceDetails =>
            _salesInvoiceDetails ??= new GenericRepository<SalesInvoiceDetail>(_context);

        public IGenericRepository<SalesReturn> SalesReturns =>
            _salesReturns ??= new GenericRepository<SalesReturn>(_context);

        public IGenericRepository<PurchaseReturn> PurchaseReturns =>
            _purchaseReturns ??= new GenericRepository<PurchaseReturn>(_context);

        public IGenericRepository<Expense> Expenses =>
            _expenses ??= new GenericRepository<Expense>(_context);

        // Specialized repository properties
        public IUserRepository Users =>
            _users ??= new UserRepository(_context);

        public IProductRepository Products =>
            _products ??= new ProductRepository(_context);

        public ISalesRepository SalesInvoices =>
            _salesInvoices ??= new SalesRepository(_context);

        public IPurchaseRepository PurchaseInvoices =>
            _purchaseInvoices ??= new PurchaseRepository(_context);

        public IInventoryRepository InventoryLogs =>
            _inventoryLogs ??= new InventoryRepository(_context);

        // Views (read-only)
        public IGenericRepository<InventoryView> InventoryViews =>
            _inventoryViews ??= new GenericRepository<InventoryView>(_context);

        public IGenericRepository<InventoryValuationView> InventoryValuationViews =>
            _inventoryValuationViews ??= new GenericRepository<InventoryValuationView>(_context);

        public IGenericRepository<CogsView> CogsViews =>
            _cogsViews ??= new GenericRepository<CogsView>(_context);

        // Transaction management
        public bool HasActiveTransaction => _transaction != null;

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess)
        {
            return await _context.SaveChangesAsync(acceptAllChangesOnSuccess);
        }

        public async Task<int> BulkSaveChangesAsync()
        {
            // For bulk operations, we might want to disable change tracking temporarily
            var originalAutoDetectChanges = _context.ChangeTracker.AutoDetectChangesEnabled;
            try
            {
                _context.ChangeTracker.AutoDetectChangesEnabled = false;
                return await _context.SaveChangesAsync();
            }
            finally
            {
                _context.ChangeTracker.AutoDetectChangesEnabled = originalAutoDetectChanges;
            }
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
                throw new InvalidOperationException("A transaction is already active.");

            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        // Advanced transaction features
        public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation)
        {
            var wasTransactionStarted = false;

            if (_transaction == null)
            {
                await BeginTransactionAsync();
                wasTransactionStarted = true;
            }

            try
            {
                var result = await operation();

                if (wasTransactionStarted)
                    await CommitTransactionAsync();

                return result;
            }
            catch
            {
                if (wasTransactionStarted)
                    await RollbackTransactionAsync();
                throw;
            }
        }

        public async Task ExecuteInTransactionAsync(Func<Task> operation)
        {
            await ExecuteInTransactionAsync(async () =>
            {
                await operation();
                return true;
            });
        }

        // Context management
        public void DetachAllEntities()
        {
            var entries = _context.ChangeTracker.Entries()
                .Where(e => e.State != EntityState.Detached)
                .ToList();

            foreach (var entry in entries)
            {
                entry.State = EntityState.Detached;
            }
        }

        public void ReloadEntity<T>(T entity) where T : class
        {
            _context.Entry(entity).Reload();
        }

        public async Task ReloadEntityAsync<T>(T entity) where T : class
        {
            await _context.Entry(entity).ReloadAsync();
        }

        // Performance monitoring
        public async Task<Dictionary<string, object>> GetPerformanceMetricsAsync()
        {
            var metrics = new Dictionary<string, object>();

            // Get change tracker statistics
            var entries = _context.ChangeTracker.Entries().ToList();
            metrics["TrackedEntities"] = entries.Count;
            metrics["AddedEntities"] = entries.Count(e => e.State == EntityState.Added);
            metrics["ModifiedEntities"] = entries.Count(e => e.State == EntityState.Modified);
            metrics["DeletedEntities"] = entries.Count(e => e.State == EntityState.Deleted);
            metrics["UnchangedEntities"] = entries.Count(e => e.State == EntityState.Unchanged);

            // Database connection info
            metrics["DatabaseProvider"] = _context.Database.ProviderName;
            metrics["HasActiveTransaction"] = HasActiveTransaction;

            // Get some basic statistics
            metrics["TotalProducts"] = await _context.Products.CountAsync();
            metrics["TotalUsers"] = await _context.Users.CountAsync();
            metrics["TotalSales"] = await _context.SalesInvoices.CountAsync();

            return metrics;
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}

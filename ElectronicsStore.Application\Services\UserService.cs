using BCrypt.Net;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;

        public UserService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<UserDto?> GetUserByIdAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdWithIncludeAsync(id, u => u.Role);
            if (user == null)
                return null;

            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                RoleId = user.RoleId,
                RoleName = user.Role.Name,
                CreatedAt = user.CreatedAt
            };
        }

        public async Task<UserDto?> GetUserByUsernameAsync(string username)
        {
            var users = await _unitOfWork.Users.GetWithIncludeAsync(u => u.Role);
            var user = users.FirstOrDefault(u => u.Username == username);
            
            if (user == null)
                return null;

            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                RoleId = user.RoleId,
                RoleName = user.Role.Name,
                CreatedAt = user.CreatedAt
            };
        }

        public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
        {
            var users = await _unitOfWork.Users.GetWithIncludeAsync(u => u.Role);
            return users.Select(u => new UserDto
            {
                Id = u.Id,
                Username = u.Username,
                RoleId = u.RoleId,
                RoleName = u.Role.Name,
                CreatedAt = u.CreatedAt
            });
        }

        public async Task<UserDto> CreateUserAsync(CreateUserDto createUserDto)
        {
            // Check if username already exists
            var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == createUserDto.Username);
            if (existingUser != null)
                throw new InvalidOperationException($"Username '{createUserDto.Username}' already exists.");

            // Check if role exists
            var roleExists = await _unitOfWork.Roles.AnyAsync(r => r.Id == createUserDto.RoleId);
            if (!roleExists)
                throw new InvalidOperationException($"Role with ID {createUserDto.RoleId} not found.");

            var user = new User
            {
                Username = createUserDto.Username,
                Password = HashPassword(createUserDto.Password),
                RoleId = createUserDto.RoleId,
                CreatedAt = DateTime.Now
            };

            await _unitOfWork.Users.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Get the created user with role
            var createdUser = await _unitOfWork.Users.GetByIdWithIncludeAsync(user.Id, u => u.Role);
            
            return new UserDto
            {
                Id = createdUser!.Id,
                Username = createdUser.Username,
                RoleId = createdUser.RoleId,
                RoleName = createdUser.Role.Name,
                CreatedAt = createdUser.CreatedAt
            };
        }

        public async Task<UserDto> UpdateUserAsync(UpdateUserDto updateUserDto)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(updateUserDto.Id);
            if (user == null)
                throw new InvalidOperationException($"User with ID {updateUserDto.Id} not found.");

            // Check if username already exists (excluding current user)
            var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == updateUserDto.Username && u.Id != updateUserDto.Id);
            if (existingUser != null)
                throw new InvalidOperationException($"Username '{updateUserDto.Username}' already exists.");

            // Check if role exists
            var roleExists = await _unitOfWork.Roles.AnyAsync(r => r.Id == updateUserDto.RoleId);
            if (!roleExists)
                throw new InvalidOperationException($"Role with ID {updateUserDto.RoleId} not found.");

            user.Username = updateUserDto.Username;
            user.RoleId = updateUserDto.RoleId;

            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();

            // Get the updated user with role
            var updatedUser = await _unitOfWork.Users.GetByIdWithIncludeAsync(user.Id, u => u.Role);

            return new UserDto
            {
                Id = updatedUser!.Id,
                Username = updatedUser.Username,
                RoleId = updatedUser.RoleId,
                RoleName = updatedUser.Role.Name,
                CreatedAt = updatedUser.CreatedAt
            };
        }

        public async Task<bool> DeleteUserAsync(int id)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(id);
            if (user == null)
                return false;

            // Check if user has related records (sales, purchases, etc.)
            var hasSales = await _unitOfWork.SalesInvoices.AnyAsync(s => s.UserId == id);
            var hasPurchases = await _unitOfWork.PurchaseInvoices.AnyAsync(p => p.UserId == id);
            var hasInventoryLogs = await _unitOfWork.InventoryLogs.AnyAsync(i => i.UserId == id);

            if (hasSales || hasPurchases || hasInventoryLogs)
                throw new InvalidOperationException("Cannot delete user that has related records.");

            _unitOfWork.Users.Remove(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ChangePasswordAsync(ChangePasswordDto changePasswordDto)
        {
            var user = await _unitOfWork.Users.GetByIdAsync(changePasswordDto.UserId);
            if (user == null)
                return false;

            // Verify current password
            if (!VerifyPassword(changePasswordDto.CurrentPassword, user.Password))
                throw new InvalidOperationException("Current password is incorrect.");

            user.Password = HashPassword(changePasswordDto.NewPassword);
            _unitOfWork.Users.Update(user);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ValidateUserCredentialsAsync(string username, string password)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Username == username);
            if (user == null)
                return false;

            return VerifyPassword(password, user.Password);
        }

        public async Task<bool> UserExistsAsync(int id)
        {
            return await _unitOfWork.Users.AnyAsync(u => u.Id == id);
        }

        public async Task<bool> UsernameExistsAsync(string username, int? excludeId = null)
        {
            if (excludeId.HasValue)
                return await _unitOfWork.Users.AnyAsync(u => u.Username == username && u.Id != excludeId.Value);

            return await _unitOfWork.Users.AnyAsync(u => u.Username == username);
        }

        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        public bool VerifyPassword(string password, string hashedPassword)
        {
            return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
        }
    }
}

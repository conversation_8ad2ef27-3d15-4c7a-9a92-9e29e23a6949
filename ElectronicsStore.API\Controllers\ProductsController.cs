using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ProductsController : ControllerBase
    {
        private readonly IProductService _productService;

        public ProductsController(IProductService productService)
        {
            _productService = productService;
        }

        /// <summary>
        /// Get all products
        /// </summary>
        /// <returns>List of products</returns>
        [HttpGet]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetProducts()
        {
            var products = await _productService.GetAllProductsAsync();
            return Ok(products);
        }

        /// <summary>
        /// Get product by ID
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <returns>Product details</returns>
        [HttpGet("{id}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<ProductDto>> GetProduct(int id)
        {
            var product = await _productService.GetProductByIdAsync(id);
            if (product == null)
                return NotFound($"Product with ID {id} not found.");

            return Ok(product);
        }

        /// <summary>
        /// Get product by barcode
        /// </summary>
        /// <param name="barcode">Product barcode</param>
        /// <returns>Product details</returns>
        [HttpGet("barcode/{barcode}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<ProductDto>> GetProductByBarcode(string barcode)
        {
            var product = await _productService.GetProductByBarcodeAsync(barcode);
            if (product == null)
                return NotFound($"Product with barcode '{barcode}' not found.");

            return Ok(product);
        }

        /// <summary>
        /// Search products
        /// </summary>
        /// <param name="searchDto">Search criteria</param>
        /// <returns>List of matching products</returns>
        [HttpPost("search")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<IEnumerable<ProductDto>>> SearchProducts(ProductSearchDto searchDto)
        {
            var products = await _productService.SearchProductsAsync(searchDto);
            return Ok(products);
        }

        /// <summary>
        /// Get products by category
        /// </summary>
        /// <param name="categoryId">Category ID</param>
        /// <returns>List of products in category</returns>
        [HttpGet("category/{categoryId}")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetProductsByCategory(int categoryId)
        {
            var products = await _productService.GetProductsByCategoryAsync(categoryId);
            return Ok(products);
        }

        /// <summary>
        /// Create a new product
        /// </summary>
        /// <param name="createProductDto">Product creation data</param>
        /// <returns>Created product</returns>
        [HttpPost]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ProductDto>> CreateProduct(CreateProductDto createProductDto)
        {
            try
            {
                var currentUserIdClaim = User.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(currentUserIdClaim) || !int.TryParse(currentUserIdClaim, out int currentUserId))
                    return Unauthorized("Invalid token.");

                var product = await _productService.CreateProductAsync(createProductDto, currentUserId);
                return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, product);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Update an existing product
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <param name="updateProductDto">Product update data</param>
        /// <returns>Updated product</returns>
        [HttpPut("{id}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ProductDto>> UpdateProduct(int id, UpdateProductDto updateProductDto)
        {
            if (id != updateProductDto.Id)
                return BadRequest("ID mismatch.");

            try
            {
                var product = await _productService.UpdateProductAsync(updateProductDto);
                return Ok(product);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Delete a product
        /// </summary>
        /// <param name="id">Product ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id}")]
        [Authorize(Roles = "admin")]
        public async Task<IActionResult> DeleteProduct(int id)
        {
            try
            {
                var result = await _productService.DeleteProductAsync(id);
                if (!result)
                    return NotFound($"Product with ID {id} not found.");

                return NoContent();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get stock summary
        /// </summary>
        /// <returns>Stock summary for all products</returns>
        [HttpGet("stock/summary")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ProductStockDto>>> GetStockSummary()
        {
            var stockSummary = await _productService.GetStockSummaryAsync();
            return Ok(stockSummary);
        }

        /// <summary>
        /// Get low stock products
        /// </summary>
        /// <param name="threshold">Low stock threshold</param>
        /// <returns>List of low stock products</returns>
        [HttpGet("stock/low")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ProductStockDto>>> GetLowStockProducts([FromQuery] int threshold = 10)
        {
            var lowStockProducts = await _productService.GetLowStockProductsAsync(threshold);
            return Ok(lowStockProducts);
        }

        /// <summary>
        /// Get out of stock products
        /// </summary>
        /// <returns>List of out of stock products</returns>
        [HttpGet("stock/out")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ProductStockDto>>> GetOutOfStockProducts()
        {
            var outOfStockProducts = await _productService.GetOutOfStockProductsAsync();
            return Ok(outOfStockProducts);
        }

        /// <summary>
        /// Adjust product stock
        /// </summary>
        /// <param name="productId">Product ID</param>
        /// <param name="quantity">Adjustment quantity (can be negative)</param>
        /// <param name="reason">Reason for adjustment</param>
        /// <returns>Success status</returns>
        [HttpPost("{productId}/adjust-stock")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> AdjustStock(int productId, [FromBody] object request)
        {
            try
            {
                var currentUserIdClaim = User.FindFirst("UserId")?.Value;
                if (string.IsNullOrEmpty(currentUserIdClaim) || !int.TryParse(currentUserIdClaim, out int currentUserId))
                    return Unauthorized("Invalid token.");

                // Parse request body
                var requestData = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(request.ToString()!);
                var quantity = Convert.ToInt32(requestData!["quantity"]);
                var reason = requestData["reason"].ToString() ?? "";

                var result = await _productService.AdjustStockAsync(productId, quantity, reason, currentUserId);
                if (!result)
                    return NotFound($"Product with ID {productId} not found.");

                return Ok(new { Message = "Stock adjusted successfully." });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Get top selling products
        /// </summary>
        /// <param name="count">Number of products to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>List of top selling products</returns>
        [HttpGet("top-selling")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ProductDto>>> GetTopSellingProducts(
            [FromQuery] int count = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var topProducts = await _productService.GetTopSellingProductsAsync(count, fromDate, toDate);
            return Ok(topProducts);
        }

        /// <summary>
        /// Get product statistics
        /// </summary>
        /// <returns>Product statistics</returns>
        [HttpGet("statistics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetProductStatistics()
        {
            var statistics = await _productService.GetProductStatisticsAsync();
            return Ok(statistics);
        }

        /// <summary>
        /// Get paged products
        /// </summary>
        /// <param name="pageNumber">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <param name="searchDto">Search criteria (optional)</param>
        /// <returns>Paged list of products</returns>
        [HttpGet("paged")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<object>> GetPagedProducts(
            [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10, [FromQuery] ProductSearchDto? searchDto = null)
        {
            var (products, totalCount) = await _productService.GetPagedProductsAsync(pageNumber, pageSize, searchDto);
            
            return Ok(new
            {
                Products = products,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }

        /// <summary>
        /// Check if barcode exists
        /// </summary>
        /// <param name="barcode">Barcode to check</param>
        /// <param name="excludeId">Product ID to exclude from check (for updates)</param>
        /// <returns>Availability status</returns>
        [HttpGet("check-barcode/{barcode}")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> CheckBarcode(string barcode, [FromQuery] int? excludeId = null)
        {
            var exists = await _productService.BarcodeExistsAsync(barcode, excludeId);
            return Ok(new { Barcode = barcode, Exists = exists, Available = !exists });
        }
    }
}

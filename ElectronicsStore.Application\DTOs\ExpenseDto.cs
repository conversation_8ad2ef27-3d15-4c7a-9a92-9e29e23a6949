namespace ElectronicsStore.Application.DTOs
{
    public class ExpenseDto
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Category { get; set; } = string.Empty;
        public DateTime ExpenseDate { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public string? ReceiptNumber { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class CreateExpenseDto
    {
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Category { get; set; } = string.Empty;
        public DateTime ExpenseDate { get; set; }
        public int UserId { get; set; }
        public string? Notes { get; set; }
        public string? ReceiptNumber { get; set; }
    }

    public class UpdateExpenseDto
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Category { get; set; } = string.Empty;
        public DateTime ExpenseDate { get; set; }
        public string? Notes { get; set; }
        public string? ReceiptNumber { get; set; }
    }

    public class ExpenseSearchDto
    {
        public string? SearchTerm { get; set; }
        public string? Category { get; set; }
        public int? UserId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
    }

    public class ExpenseReportDto
    {
        public DateTime Date { get; set; }
        public int TotalExpenses { get; set; }
        public decimal TotalAmount { get; set; }
        public string Category { get; set; } = string.Empty;
    }

    public class ExpenseCategoryDto
    {
        public string Category { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal Percentage { get; set; }
    }

    public class MonthlyExpenseDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int TotalExpenses { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }
    }
}

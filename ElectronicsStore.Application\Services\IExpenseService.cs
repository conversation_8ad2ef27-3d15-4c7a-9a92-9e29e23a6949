using ElectronicsStore.Application.DTOs;

namespace ElectronicsStore.Application.Services
{
    public interface IExpenseService
    {
        // Basic CRUD operations
        Task<ExpenseDto?> GetExpenseByIdAsync(int id);
        Task<IEnumerable<ExpenseDto>> GetAllExpensesAsync();
        Task<ExpenseDto> CreateExpenseAsync(CreateExpenseDto createExpenseDto);
        Task<ExpenseDto> UpdateExpenseAsync(UpdateExpenseDto updateExpenseDto);
        Task<bool> DeleteExpenseAsync(int id);

        // Search and filtering
        Task<IEnumerable<ExpenseDto>> SearchExpensesAsync(ExpenseSearchDto searchDto);
        Task<IEnumerable<ExpenseDto>> GetExpensesByCategoryAsync(string category);
        Task<IEnumerable<ExpenseDto>> GetExpensesByUserAsync(int userId);
        Task<IEnumerable<ExpenseDto>> GetExpensesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<ExpenseDto>> GetTodaysExpensesAsync();

        // Expense analytics
        Task<decimal> GetTotalExpenseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetTotalExpenseCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAverageExpenseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Reports
        Task<IEnumerable<ExpenseReportDto>> GetDailyExpenseReportAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<ExpenseReportDto>> GetMonthlyExpenseReportAsync(int year);
        Task<IEnumerable<ExpenseCategoryDto>> GetExpensesByCategoryReportAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Categories
        Task<IEnumerable<string>> GetExpenseCategoriesAsync();
        Task<IEnumerable<ExpenseDto>> GetExpensesByMonthAsync(int year, int month);

        // Validation
        Task<bool> ExpenseExistsAsync(int id);
        Task<bool> CanDeleteExpenseAsync(int id);

        // Pagination
        Task<(IEnumerable<ExpenseDto> Expenses, int TotalCount)> GetPagedExpensesAsync(
            int pageNumber, int pageSize, ExpenseSearchDto? searchDto = null);

        // Statistics
        Task<object> GetExpenseStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }
}

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Services;
using AutoMapper;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly IReportService _reportService;
        private readonly IMapper _mapper;

        public ReportsController(IReportService reportService, IMapper mapper)
        {
            _reportService = reportService;
            _mapper = mapper;
        }

        /// <summary>
        /// Get financial summary
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Financial summary report</returns>
        [HttpGet("financial/summary")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetFinancialSummary([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var summary = await _reportService.GetFinancialSummaryAsync(fromDate, toDate);
            return Ok(summary);
        }

        /// <summary>
        /// Get profit and loss report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Profit and loss report</returns>
        [HttpGet("financial/profit-loss")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetProfitLossReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetProfitLossReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get cash flow report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Cash flow report</returns>
        [HttpGet("financial/cash-flow")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetCashFlowReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetCashFlowReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get sales report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Sales report</returns>
        [HttpGet("sales")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<SalesReportDto>>> GetSalesReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetSalesReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get top customers report
        /// </summary>
        /// <param name="count">Number of customers to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Top customers report</returns>
        [HttpGet("sales/top-customers")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<TopCustomerDto>>> GetTopCustomersReport(
            [FromQuery] int count = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var report = await _reportService.GetTopCustomersReportAsync(count, fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get top products report
        /// </summary>
        /// <param name="count">Number of products to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Top products report</returns>
        [HttpGet("sales/top-products")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<TopProductDto>>> GetTopProductsReport(
            [FromQuery] int count = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var report = await _reportService.GetTopProductsReportAsync(count, fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get sales performance report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Sales performance report</returns>
        [HttpGet("sales/performance")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetSalesPerformanceReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetSalesPerformanceReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get purchase report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Purchase report</returns>
        [HttpGet("purchases")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPurchaseReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetPurchaseReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get top suppliers report
        /// </summary>
        /// <param name="count">Number of suppliers to return</param>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Top suppliers report</returns>
        [HttpGet("purchases/top-suppliers")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetTopSuppliersReport(
            [FromQuery] int count = 10, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var report = await _reportService.GetTopSuppliersReportAsync(count, fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get purchase analysis report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Purchase analysis report</returns>
        [HttpGet("purchases/analysis")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetPurchaseAnalysisReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetPurchaseAnalysisReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get inventory valuation report
        /// </summary>
        /// <returns>Inventory valuation report</returns>
        [HttpGet("inventory/valuation")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<InventoryValuationDto>>> GetInventoryValuationReport()
        {
            var report = await _reportService.GetInventoryValuationReportAsync();
            return Ok(report);
        }

        /// <summary>
        /// Get stock alerts report
        /// </summary>
        /// <returns>Stock alerts report</returns>
        [HttpGet("inventory/stock-alerts")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<LowStockAlertDto>>> GetStockAlertsReport()
        {
            var report = await _reportService.GetStockAlertsReportAsync();
            return Ok(report);
        }

        /// <summary>
        /// Get inventory movement report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Inventory movement report</returns>
        [HttpGet("inventory/movements")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetInventoryMovementReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetInventoryMovementReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get slow moving products report
        /// </summary>
        /// <param name="days">Number of days to consider for slow moving</param>
        /// <returns>Slow moving products report</returns>
        [HttpGet("inventory/slow-moving")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetSlowMovingProductsReport([FromQuery] int days = 90)
        {
            var report = await _reportService.GetSlowMovingProductsReportAsync(days);
            return Ok(report);
        }

        /// <summary>
        /// Get expense report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Expense report</returns>
        [HttpGet("expenses")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseReportDto>>> GetExpenseReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetExpenseReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get expense category report
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Expense category report</returns>
        [HttpGet("expenses/categories")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<IEnumerable<ExpenseCategoryDto>>> GetExpenseCategoryReport([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var report = await _reportService.GetExpenseCategoryReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Get dashboard summary
        /// </summary>
        /// <returns>Dashboard summary</returns>
        [HttpGet("dashboard/summary")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<object>> GetDashboardSummary()
        {
            var summary = await _reportService.GetDashboardSummaryAsync();
            return Ok(summary);
        }

        /// <summary>
        /// Get dashboard charts data
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>Dashboard charts data</returns>
        [HttpGet("dashboard/charts")]
        [Authorize(Roles = "admin,manager,cashier")]
        public async Task<ActionResult<object>> GetDashboardCharts([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var charts = await _reportService.GetDashboardChartsAsync(fromDate, toDate);
            return Ok(charts);
        }

        /// <summary>
        /// Get KPI report
        /// </summary>
        /// <param name="fromDate">Start date (optional)</param>
        /// <param name="toDate">End date (optional)</param>
        /// <returns>KPI report</returns>
        [HttpGet("kpi")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetKPIReport([FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
        {
            var kpi = await _reportService.GetKPIReportAsync(fromDate, toDate);
            return Ok(kpi);
        }

        /// <summary>
        /// Get year over year comparison
        /// </summary>
        /// <param name="year1">First year</param>
        /// <param name="year2">Second year</param>
        /// <returns>Year over year comparison</returns>
        [HttpGet("comparison/year-over-year")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetYearOverYearComparison([FromQuery] int year1, [FromQuery] int year2)
        {
            var comparison = await _reportService.GetYearOverYearComparisonAsync(year1, year2);
            return Ok(comparison);
        }

        /// <summary>
        /// Get user performance report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>User performance report</returns>
        [HttpGet("users/performance")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<object>> GetUserPerformanceReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            var report = await _reportService.GetUserPerformanceReportAsync(fromDate, toDate);
            return Ok(report);
        }

        /// <summary>
        /// Export report to PDF
        /// </summary>
        /// <param name="reportType">Report type</param>
        /// <param name="parameters">Report parameters</param>
        /// <returns>PDF file</returns>
        [HttpPost("export/pdf")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> ExportReportToPdf([FromBody] Dictionary<string, object> request)
        {
            try
            {
                var reportType = request["reportType"].ToString() ?? "";
                var parameters = request.ContainsKey("parameters") ? 
                    (Dictionary<string, object>)request["parameters"] : 
                    new Dictionary<string, object>();

                var pdfBytes = await _reportService.ExportReportToPdfAsync(reportType, parameters);
                return File(pdfBytes, "application/pdf", $"{reportType}_report.pdf");
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Export report to Excel
        /// </summary>
        /// <param name="reportType">Report type</param>
        /// <param name="parameters">Report parameters</param>
        /// <returns>Excel file</returns>
        [HttpPost("export/excel")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> ExportReportToExcel([FromBody] Dictionary<string, object> request)
        {
            try
            {
                var reportType = request["reportType"].ToString() ?? "";
                var parameters = request.ContainsKey("parameters") ? 
                    (Dictionary<string, object>)request["parameters"] : 
                    new Dictionary<string, object>();

                var excelBytes = await _reportService.ExportReportToExcelAsync(reportType, parameters);
                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"{reportType}_report.xlsx");
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}

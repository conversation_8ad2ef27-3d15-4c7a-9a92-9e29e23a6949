using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ElectronicsStore.Application.DTOs;
using ElectronicsStore.Application.Interfaces;

namespace ElectronicsStore.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReportsController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public ReportsController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Get dashboard summary
        /// </summary>
        /// <returns>Dashboard data with key metrics</returns>
        [HttpGet("dashboard")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<DashboardDto>> GetDashboard()
        {
            try
            {
                var today = DateTime.Today;
                var thisMonth = new DateTime(today.Year, today.Month, 1);

                // Get sales data
                var sales = await _unitOfWork.Sales.GetAllAsync();
                var thisMonthSales = sales.Where(s => s.InvoiceDate >= thisMonth);

                // Get purchases data
                var purchases = await _unitOfWork.PurchaseInvoices.GetAllAsync();
                var thisMonthPurchases = purchases.Where(p => p.InvoiceDate >= thisMonth);

                // Get expenses data
                var expenses = await _unitOfWork.Expenses.GetAllAsync();
                var thisMonthExpenses = expenses.Where(e => e.ExpenseDate >= thisMonth);

                // Get products data
                var products = await _unitOfWork.Products.GetAllAsync();

                // Get inventory data
                var inventoryLogs = await _unitOfWork.Inventory.GetAllAsync();

                var dashboard = new DashboardDto
                {
                    TotalSales = thisMonthSales.Sum(s => s.TotalAmount),
                    TotalPurchases = thisMonthPurchases.Sum(p => p.TotalAmount),
                    TotalExpenses = thisMonthExpenses.Sum(e => e.Amount),
                    NetProfit = thisMonthSales.Sum(s => s.TotalAmount) - thisMonthPurchases.Sum(p => p.TotalAmount) - thisMonthExpenses.Sum(e => e.Amount),
                    TotalProducts = products.Count(),
                    LowStockProducts = 0, // Will be calculated based on inventory
                    OutOfStockProducts = 0, // Will be calculated based on inventory
                    InventoryValue = 0, // Will be calculated based on inventory
                    RecentSales = thisMonthSales.GroupBy(s => s.InvoiceDate.Date)
                        .Select(g => new DailySalesDto
                        {
                            Date = g.Key,
                            Amount = g.Sum(s => s.TotalAmount),
                            Count = g.Count()
                        })
                        .OrderByDescending(d => d.Date)
                        .Take(7)
                        .ToList(),
                    TopProducts = new List<TopProductDto>(),
                    ExpenseBreakdown = thisMonthExpenses.GroupBy(e => e.Category)
                        .Select(g => new ExpenseCategoryDto
                        {
                            Category = g.Key,
                            Count = g.Count(),
                            TotalAmount = g.Sum(e => e.Amount),
                            AverageAmount = g.Average(e => e.Amount),
                            Percentage = thisMonthExpenses.Sum(e => e.Amount) > 0 ? 
                                (g.Sum(e => e.Amount) / thisMonthExpenses.Sum(e => e.Amount)) * 100 : 0
                        })
                        .ToList()
                };

                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error generating dashboard: {ex.Message}");
            }
        }

        /// <summary>
        /// Get financial report
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Financial report with profit/loss analysis</returns>
        [HttpGet("financial")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<FinancialReportDto>> GetFinancialReport([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            try
            {
                var sales = await _unitOfWork.Sales.GetAllAsync();
                var purchases = await _unitOfWork.PurchaseInvoices.GetAllAsync();
                var expenses = await _unitOfWork.Expenses.GetAllAsync();

                var periodSales = sales.Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate);
                var periodPurchases = purchases.Where(p => p.InvoiceDate >= fromDate && p.InvoiceDate <= toDate);
                var periodExpenses = expenses.Where(e => e.ExpenseDate >= fromDate && e.ExpenseDate <= toDate);

                var totalRevenue = periodSales.Sum(s => s.TotalAmount);
                var totalCosts = periodPurchases.Sum(p => p.TotalAmount);
                var totalExpenses = periodExpenses.Sum(e => e.Amount);
                var grossProfit = totalRevenue - totalCosts;
                var netProfit = grossProfit - totalExpenses;

                var report = new FinancialReportDto
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalRevenue = totalRevenue,
                    TotalCosts = totalCosts,
                    TotalExpenses = totalExpenses,
                    GrossProfit = grossProfit,
                    NetProfit = netProfit,
                    ProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0,
                    MonthlyBreakdown = new List<MonthlyFinancialDto>()
                };

                // Generate monthly breakdown
                var currentDate = new DateTime(fromDate.Year, fromDate.Month, 1);
                var endDate = new DateTime(toDate.Year, toDate.Month, 1);

                while (currentDate <= endDate)
                {
                    var monthStart = currentDate;
                    var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                    var monthSales = periodSales.Where(s => s.InvoiceDate >= monthStart && s.InvoiceDate <= monthEnd);
                    var monthPurchases = periodPurchases.Where(p => p.InvoiceDate >= monthStart && p.InvoiceDate <= monthEnd);
                    var monthExpenses = periodExpenses.Where(e => e.ExpenseDate >= monthStart && e.ExpenseDate <= monthEnd);

                    var monthRevenue = monthSales.Sum(s => s.TotalAmount);
                    var monthCosts = monthPurchases.Sum(p => p.TotalAmount);
                    var monthExpensesTotal = monthExpenses.Sum(e => e.Amount);

                    report.MonthlyBreakdown.Add(new MonthlyFinancialDto
                    {
                        Year = currentDate.Year,
                        Month = currentDate.Month,
                        MonthName = currentDate.ToString("MMMM yyyy"),
                        Revenue = monthRevenue,
                        Costs = monthCosts,
                        Expenses = monthExpensesTotal,
                        Profit = monthRevenue - monthCosts - monthExpensesTotal
                    });

                    currentDate = currentDate.AddMonths(1);
                }

                return Ok(report);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error generating financial report: {ex.Message}");
            }
        }

        /// <summary>
        /// Get inventory report
        /// </summary>
        /// <returns>Inventory status and valuation report</returns>
        [HttpGet("inventory")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<InventoryReportDto>> GetInventoryReport()
        {
            try
            {
                var products = await _unitOfWork.Products.GetAllAsync();
                var categories = await _unitOfWork.Categories.GetAllAsync();
                var inventoryLogs = await _unitOfWork.Inventory.GetAllAsync();

                var report = new InventoryReportDto
                {
                    TotalProducts = products.Count(),
                    TotalValue = 0,
                    LowStockCount = 0,
                    OutOfStockCount = 0,
                    LowStockProducts = new List<ProductStockDto>(),
                    OutOfStockProducts = new List<ProductStockDto>(),
                    CategoryBreakdown = categories.Select(c => new CategoryStockDto
                    {
                        CategoryId = c.Id,
                        CategoryName = c.Name,
                        ProductCount = products.Count(p => p.CategoryId == c.Id),
                        TotalStock = 0,
                        TotalValue = 0
                    }).ToList()
                };

                // Calculate stock levels for each product
                foreach (var product in products)
                {
                    var productLogs = inventoryLogs.Where(il => il.ProductId == product.Id);
                    var currentStock = productLogs.Sum(il => il.Quantity);
                    var stockValue = currentStock * product.DefaultCostPrice;

                    report.TotalValue += stockValue;

                    // Update category breakdown
                    var categoryBreakdown = report.CategoryBreakdown.FirstOrDefault(c => c.CategoryId == product.CategoryId);
                    if (categoryBreakdown != null)
                    {
                        categoryBreakdown.TotalStock += currentStock;
                        categoryBreakdown.TotalValue += stockValue;
                    }

                    // Check for low stock or out of stock
                    if (currentStock <= 0)
                    {
                        report.OutOfStockCount++;
                        report.OutOfStockProducts.Add(new ProductStockDto
                        {
                            ProductId = product.Id,
                            ProductName = product.Name,
                            CurrentQuantity = currentStock,
                            Status = "OutOfStock"
                        });
                    }
                    else if (currentStock <= 10) // Assuming 10 is the low stock threshold
                    {
                        report.LowStockCount++;
                        report.LowStockProducts.Add(new ProductStockDto
                        {
                            ProductId = product.Id,
                            ProductName = product.Name,
                            CurrentQuantity = currentStock,
                            Status = "LowStock"
                        });
                    }
                }

                return Ok(report);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error generating inventory report: {ex.Message}");
            }
        }

        /// <summary>
        /// Get sales analytics
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Sales analytics and trends</returns>
        [HttpGet("sales-analytics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<SalesAnalyticsDto>> GetSalesAnalytics([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            try
            {
                var sales = await _unitOfWork.Sales.GetAllAsync();
                var periodSales = sales.Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate);

                var analytics = new SalesAnalyticsDto
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalSales = periodSales.Count(),
                    TotalRevenue = periodSales.Sum(s => s.TotalAmount),
                    AverageSaleAmount = periodSales.Any() ? periodSales.Average(s => s.TotalAmount) : 0,
                    TopCustomers = new List<TopCustomerDto>(),
                    TopProducts = new List<TopProductDto>(),
                    DailySales = periodSales.GroupBy(s => s.InvoiceDate.Date)
                        .Select(g => new SalesReportDto
                        {
                            Date = g.Key,
                            TotalSales = g.Count(),
                            TotalAmount = g.Sum(s => s.TotalAmount),
                            TotalDiscount = g.Sum(s => s.DiscountTotal),
                            NetAmount = g.Sum(s => s.TotalAmount - s.DiscountTotal)
                        })
                        .OrderBy(d => d.Date)
                        .ToList(),
                    PaymentMethodBreakdown = periodSales.GroupBy(s => s.PaymentMethod)
                        .ToDictionary(g => g.Key, g => g.Sum(s => s.TotalAmount))
                };

                return Ok(analytics);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error generating sales analytics: {ex.Message}");
            }
        }

        /// <summary>
        /// Get purchase analytics
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Purchase analytics and trends</returns>
        [HttpGet("purchase-analytics")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<PurchaseAnalyticsDto>> GetPurchaseAnalytics([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            try
            {
                var purchases = await _unitOfWork.PurchaseInvoices.GetAllAsync();
                var periodPurchases = purchases.Where(p => p.InvoiceDate >= fromDate && p.InvoiceDate <= toDate);

                var analytics = new PurchaseAnalyticsDto
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalPurchases = periodPurchases.Count(),
                    TotalAmount = periodPurchases.Sum(p => p.TotalAmount),
                    AveragePurchaseAmount = periodPurchases.Any() ? periodPurchases.Average(p => p.TotalAmount) : 0,
                    TopSuppliers = new List<TopSupplierDto>(),
                    DailyPurchases = periodPurchases.GroupBy(p => p.InvoiceDate.Date)
                        .Select(g => new PurchaseReportDto
                        {
                            Date = g.Key,
                            TotalPurchases = g.Count(),
                            TotalAmount = g.Sum(p => p.TotalAmount),
                            TotalItems = g.SelectMany(p => p.PurchaseInvoiceDetails ?? new List<Domain.Entities.PurchaseInvoiceDetail>()).Sum(d => d.Quantity)
                        })
                        .OrderBy(d => d.Date)
                        .ToList()
                };

                return Ok(analytics);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error generating purchase analytics: {ex.Message}");
            }
        }

        /// <summary>
        /// Get profit analysis
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Detailed profit analysis by products</returns>
        [HttpGet("profit-analysis")]
        [Authorize(Roles = "admin,manager")]
        public async Task<ActionResult<ProfitAnalysisDto>> GetProfitAnalysis([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        {
            try
            {
                var sales = await _unitOfWork.Sales.GetAllAsync();
                var expenses = await _unitOfWork.Expenses.GetAllAsync();

                var periodSales = sales.Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate);
                var periodExpenses = expenses.Where(e => e.ExpenseDate >= fromDate && e.ExpenseDate <= toDate);

                var totalRevenue = periodSales.Sum(s => s.TotalAmount);
                var totalExpenses = periodExpenses.Sum(e => e.Amount);

                // Calculate COGS (Cost of Goods Sold) - simplified calculation
                var totalCOGS = 0m; // This would need proper calculation based on inventory movements

                var analysis = new ProfitAnalysisDto
                {
                    FromDate = fromDate,
                    ToDate = toDate,
                    TotalRevenue = totalRevenue,
                    TotalCOGS = totalCOGS,
                    GrossProfit = totalRevenue - totalCOGS,
                    GrossProfitMargin = totalRevenue > 0 ? ((totalRevenue - totalCOGS) / totalRevenue) * 100 : 0,
                    TotalExpenses = totalExpenses,
                    NetProfit = totalRevenue - totalCOGS - totalExpenses,
                    NetProfitMargin = totalRevenue > 0 ? ((totalRevenue - totalCOGS - totalExpenses) / totalRevenue) * 100 : 0,
                    ProductProfitability = new List<ProductProfitDto>()
                };

                return Ok(analysis);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error generating profit analysis: {ex.Message}");
            }
        }

        /// <summary>
        /// Export report data
        /// </summary>
        /// <param name="reportType">Type of report to export</param>
        /// <param name="format">Export format (json, csv)</param>
        /// <param name="filter">Report filters</param>
        /// <returns>Exported report data</returns>
        [HttpPost("export")]
        [Authorize(Roles = "admin,manager")]
        public async Task<IActionResult> ExportReport([FromQuery] string reportType, [FromQuery] string format, [FromBody] ReportFilterDto filter)
        {
            try
            {
                // This is a placeholder for report export functionality
                // In a real implementation, you would generate the appropriate report format
                
                var exportData = new
                {
                    ReportType = reportType,
                    Format = format,
                    GeneratedAt = DateTime.Now,
                    Filter = filter,
                    Message = "Export functionality would be implemented here"
                };

                if (format.ToLower() == "json")
                {
                    return Ok(exportData);
                }
                else if (format.ToLower() == "csv")
                {
                    // Return CSV format
                    return Ok("CSV export would be implemented here");
                }

                return BadRequest("Unsupported export format");
            }
            catch (Exception ex)
            {
                return BadRequest($"Error exporting report: {ex.Message}");
            }
        }
    }
}

using ElectronicsStore.Domain.Entities;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        // Generic repositories for basic entities
        IGenericRepository<Category> Categories { get; }
        IGenericRepository<Supplier> Suppliers { get; }
        IGenericRepository<Role> Roles { get; }
        IGenericRepository<Permission> Permissions { get; }
        IGenericRepository<RolePermission> RolePermissions { get; }
        IGenericRepository<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; }
        IGenericRepository<SalesInvoiceDetail> SalesInvoiceDetails { get; }
        IGenericRepository<SalesReturn> SalesReturns { get; }
        IGenericRepository<PurchaseReturn> PurchaseReturns { get; }
        IGenericRepository<Expense> Expenses { get; }

        // Specialized repositories with business logic
        IUserRepository Users { get; }
        IProductRepository Products { get; }
        ISalesRepository SalesInvoices { get; }
        IPurchaseRepository PurchaseInvoices { get; }
        IInventoryRepository InventoryLogs { get; }

        // Views (read-only)
        IGenericRepository<InventoryView> InventoryViews { get; }
        IGenericRepository<InventoryValuationView> InventoryValuationViews { get; }
        IGenericRepository<CogsView> CogsViews { get; }

        // Transaction management
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        bool HasActiveTransaction { get; }

        // Advanced transaction features
        Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation);
        Task ExecuteInTransactionAsync(Func<Task> operation);

        // Bulk operations
        Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess);
        Task<int> BulkSaveChangesAsync();

        // Context management
        void DetachAllEntities();
        void ReloadEntity<T>(T entity) where T : class;
        Task ReloadEntityAsync<T>(T entity) where T : class;

        // Performance monitoring
        Task<Dictionary<string, object>> GetPerformanceMetricsAsync();
    }
}

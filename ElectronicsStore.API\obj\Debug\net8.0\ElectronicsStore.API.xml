<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ElectronicsStore.API</name>
    </assembly>
    <members>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.Login(ElectronicsStore.Application.DTOs.LoginDto)">
            <summary>
            User login
            </summary>
            <param name="loginDto">Login credentials</param>
            <returns>JWT token and user information</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.GetCurrentUser">
            <summary>
            Get current authenticated user information
            </summary>
            <returns>Current user details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.RefreshToken(ElectronicsStore.Application.DTOs.RefreshTokenDto)">
            <summary>
            Refresh JWT token
            </summary>
            <param name="refreshTokenDto">Current token and refresh token</param>
            <returns>New JWT token</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.AuthController.ValidateToken(System.String)">
            <summary>
            Validate token
            </summary>
            <param name="token">JWT token to validate</param>
            <returns>Token validation result</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.GetCategories">
            <summary>
            Get all categories
            </summary>
            <returns>List of categories</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.GetCategory(System.Int32)">
            <summary>
            Get category by ID
            </summary>
            <param name="id">Category ID</param>
            <returns>Category details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.CreateCategory(ElectronicsStore.Application.DTOs.CreateCategoryDto)">
            <summary>
            Create a new category
            </summary>
            <param name="createCategoryDto">Category creation data</param>
            <returns>Created category</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.UpdateCategory(System.Int32,ElectronicsStore.Application.DTOs.UpdateCategoryDto)">
            <summary>
            Update an existing category
            </summary>
            <param name="id">Category ID</param>
            <param name="updateCategoryDto">Category update data</param>
            <returns>Updated category</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.DeleteCategory(System.Int32)">
            <summary>
            Delete a category
            </summary>
            <param name="id">Category ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.RolesController.GetRoles">
            <summary>
            Get all roles
            </summary>
            <returns>List of roles</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.RolesController.GetRole(System.Int32)">
            <summary>
            Get role by ID
            </summary>
            <param name="id">Role ID</param>
            <returns>Role details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.RolesController.CreateRole(ElectronicsStore.Domain.Entities.Role)">
            <summary>
            Create a new role
            </summary>
            <param name="role">Role data</param>
            <returns>Created role</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.GetUsers">
            <summary>
            Get all users
            </summary>
            <returns>List of users</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.GetUser(System.Int32)">
            <summary>
            Get user by ID
            </summary>
            <param name="id">User ID</param>
            <returns>User details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.CreateUser(ElectronicsStore.Application.DTOs.CreateUserDto)">
            <summary>
            Create a new user
            </summary>
            <param name="createUserDto">User creation data</param>
            <returns>Created user</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.UpdateUser(System.Int32,ElectronicsStore.Application.DTOs.UpdateUserDto)">
            <summary>
            Update an existing user
            </summary>
            <param name="id">User ID</param>
            <param name="updateUserDto">User update data</param>
            <returns>Updated user</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.DeleteUser(System.Int32)">
            <summary>
            Delete a user
            </summary>
            <param name="id">User ID</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.ChangePassword(ElectronicsStore.Application.DTOs.ChangePasswordDto)">
            <summary>
            Change user password
            </summary>
            <param name="changePasswordDto">Password change data</param>
            <returns>Success status</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.UsersController.CheckUsername(System.String,System.Nullable{System.Int32})">
            <summary>
            Check if username exists
            </summary>
            <param name="username">Username to check</param>
            <param name="excludeId">User ID to exclude from check (for updates)</param>
            <returns>Availability status</returns>
        </member>
    </members>
</doc>

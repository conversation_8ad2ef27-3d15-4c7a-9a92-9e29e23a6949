<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ElectronicsStore.API</name>
    </assembly>
    <members>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.GetCategories">
            <summary>
            Get all categories
            </summary>
            <returns>List of categories</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.GetCategory(System.Int32)">
            <summary>
            Get category by ID
            </summary>
            <param name="id">Category ID</param>
            <returns>Category details</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.CreateCategory(ElectronicsStore.Application.DTOs.CreateCategoryDto)">
            <summary>
            Create a new category
            </summary>
            <param name="createCategoryDto">Category creation data</param>
            <returns>Created category</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.UpdateCategory(System.Int32,ElectronicsStore.Application.DTOs.UpdateCategoryDto)">
            <summary>
            Update an existing category
            </summary>
            <param name="id">Category ID</param>
            <param name="updateCategoryDto">Category update data</param>
            <returns>Updated category</returns>
        </member>
        <member name="M:ElectronicsStore.API.Controllers.CategoriesController.DeleteCategory(System.Int32)">
            <summary>
            Delete a category
            </summary>
            <param name="id">Category ID</param>
            <returns>Success status</returns>
        </member>
    </members>
</doc>

{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"ElectronicsStore.Infrastructure/1.0.0": {"dependencies": {"ElectronicsStore.Application": "1.0.0", "Microsoft.Extensions.Options": "9.0.8", "System.IdentityModel.Tokens.Jwt": "8.13.0"}, "runtime": {"ElectronicsStore.Infrastructure.dll": {}}}, "BCrypt.Net-Next/4.0.3": {"runtime": {"lib/net6.0/BCrypt.Net-Next.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.IdentityModel.Abstractions/8.13.0": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.13.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.13.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "Microsoft.IdentityModel.Logging/8.13.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.13.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "Microsoft.IdentityModel.Tokens/8.13.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.IdentityModel.Logging": "8.13.0"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "System.IdentityModel.Tokens.Jwt/8.13.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.13.0", "Microsoft.IdentityModel.Tokens": "8.13.0"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "ElectronicsStore.Application/1.0.0": {"dependencies": {"BCrypt.Net-Next": "4.0.3", "ElectronicsStore.Domain": "1.0.0"}, "runtime": {"ElectronicsStore.Application.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "ElectronicsStore.Domain/1.0.0": {"runtime": {"ElectronicsStore.Domain.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"ElectronicsStore.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BCrypt.Net-Next/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W+U9WvmZQgi5cX6FS5GDtDoPzUCV4LkBLkywq/kRZhuDwcbavOzcDAr3LXJFqHUi952Yj3LEYoWW0jbEUQChsA==", "path": "bcrypt.net-next/4.0.3", "hashPath": "bcrypt.net-next.4.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-gHTIONGSrGAMu6QdUoXsqC7d+sZ3WUHuaMOxpI0SjeMrQsR1kkd5KakpG6UMl1QNFyzctWUCloC5wsG8keDkyQ==", "path": "microsoft.identitymodel.abstractions/8.13.0", "hashPath": "microsoft.identitymodel.abstractions.8.13.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-aWSosjvr4cZPqknW+8+6fs0HzD/lAIDFFtI5ajcrxI5CcCg2+YacpPi03oA+prl4sSuh16+HuI/oYq9ZgdlPqA==", "path": "microsoft.identitymodel.jsonwebtokens/8.13.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.13.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-ydRP1oDvel5JbuVL2UPWHbXEhECaBmbl7/SPx6J0UeEEiassy62glDyWXNKyKS/gb1MrWp5/Y2TQKEtBDXwPuQ==", "path": "microsoft.identitymodel.logging/8.13.0", "hashPath": "microsoft.identitymodel.logging.8.13.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-25zE+hhKJTOUPh5lxAeHgHiNPDYsPo3iJHd2JVKTalFqMJChJvOkrB5+9PsgKiNm7TpB9h2l6h4AbAl0D3H7OA==", "path": "microsoft.identitymodel.tokens/8.13.0", "hashPath": "microsoft.identitymodel.tokens.8.13.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-GA3moxioWoF/lzRRGNjz+7LD91tajHvS4S6zn3Y3G1p/Koes7pj6gZIt4rzGhe4iIn4rvdj9wxpmN6quObgfMw==", "path": "system.identitymodel.tokens.jwt/8.13.0", "hashPath": "system.identitymodel.tokens.jwt.8.13.0.nupkg.sha512"}, "ElectronicsStore.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ElectronicsStore.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}
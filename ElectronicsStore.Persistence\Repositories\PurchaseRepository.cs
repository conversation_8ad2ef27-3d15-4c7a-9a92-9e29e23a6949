using Microsoft.EntityFrameworkCore;
using ElectronicsStore.Application.Interfaces;
using ElectronicsStore.Domain.Entities;
using System.Linq.Expressions;

namespace ElectronicsStore.Persistence.Repositories
{
    public class PurchaseRepository : GenericRepository<PurchaseInvoice>, IPurchaseRepository
    {
        public PurchaseRepository(ElectronicsDbContext context) : base(context)
        {
        }

        public async Task<PurchaseInvoice?> GetWithDetailsAsync(int id)
        {
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                .Include(pi => pi.User)
                .Include(pi => pi.Supplier)
                .FirstOrDefaultAsync(pi => pi.Id == id);
        }

        public async Task<PurchaseInvoice?> GetWithDetailsAndProductsAsync(int id)
        {
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                    .ThenInclude(pid => pid.Product)
                .Include(pi => pi.User)
                .Include(pi => pi.Supplier)
                .FirstOrDefaultAsync(pi => pi.Id == id);
        }

        public async Task<PurchaseInvoice?> GetByInvoiceNumberAsync(string invoiceNumber)
        {
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                .Include(pi => pi.User)
                .Include(pi => pi.Supplier)
                .FirstOrDefaultAsync(pi => pi.InvoiceNumber == invoiceNumber);
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetBySupplierAsync(int supplierId)
        {
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                .Include(pi => pi.Supplier)
                .Where(pi => pi.SupplierId == supplierId)
                .OrderByDescending(pi => pi.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetByUserAsync(int userId)
        {
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                .Include(pi => pi.User)
                .Where(pi => pi.UserId == userId)
                .OrderByDescending(pi => pi.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                .Include(pi => pi.User)
                .Include(pi => pi.Supplier)
                .Where(pi => pi.InvoiceDate >= fromDate && pi.InvoiceDate <= toDate)
                .OrderByDescending(pi => pi.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetTodaysPurchasesAsync()
        {
            var today = DateTime.Today;
            var tomorrow = today.AddDays(1);
            
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                .Include(pi => pi.User)
                .Include(pi => pi.Supplier)
                .Where(pi => pi.InvoiceDate >= today && pi.InvoiceDate < tomorrow)
                .OrderByDescending(pi => pi.InvoiceDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalPurchaseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= toDate.Value);

            return await query.SumAsync(pi => pi.TotalAmount);
        }

        public async Task<int> GetTotalPurchaseCountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= toDate.Value);

            return await query.CountAsync();
        }

        public async Task<decimal> GetAveragePurchaseAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= toDate.Value);

            if (!await query.AnyAsync())
                return 0;

            return await query.AverageAsync(pi => pi.TotalAmount);
        }

        public async Task<IEnumerable<object>> GetDailyPurchaseReportAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Where(pi => pi.InvoiceDate >= fromDate && pi.InvoiceDate <= toDate)
                .GroupBy(pi => pi.InvoiceDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    PurchaseCount = g.Count(),
                    TotalAmount = g.Sum(pi => pi.TotalAmount),
                    AverageAmount = g.Average(pi => pi.TotalAmount)
                })
                .OrderBy(x => x.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetMonthlyPurchaseReportAsync(int year)
        {
            return await _dbSet
                .Where(pi => pi.InvoiceDate.Year == year)
                .GroupBy(pi => pi.InvoiceDate.Month)
                .Select(g => new
                {
                    Month = g.Key,
                    PurchaseCount = g.Count(),
                    TotalAmount = g.Sum(pi => pi.TotalAmount),
                    AverageAmount = g.Average(pi => pi.TotalAmount)
                })
                .OrderBy(x => x.Month)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetPurchasesBySupplierAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.Include(pi => pi.Supplier).AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(pi => new { pi.SupplierId, pi.Supplier.Name })
                .Select(g => new
                {
                    SupplierId = g.Key.SupplierId,
                    SupplierName = g.Key.Name,
                    PurchaseCount = g.Count(),
                    TotalAmount = g.Sum(pi => pi.TotalAmount),
                    AverageAmount = g.Average(pi => pi.TotalAmount)
                })
                .OrderByDescending(x => x.TotalAmount)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetTopSuppliersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.Include(pi => pi.Supplier).AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= toDate.Value);

            return await query
                .GroupBy(pi => new { pi.SupplierId, pi.Supplier.Name })
                .Select(g => new
                {
                    SupplierId = g.Key.SupplierId,
                    SupplierName = g.Key.Name,
                    PurchaseCount = g.Count(),
                    TotalAmount = g.Sum(pi => pi.TotalAmount),
                    LastPurchaseDate = g.Max(pi => pi.InvoiceDate)
                })
                .OrderByDescending(x => x.TotalAmount)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetSupplierPerformanceAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(pi => pi.Supplier)
                .Where(pi => pi.InvoiceDate >= fromDate && pi.InvoiceDate <= toDate)
                .GroupBy(pi => new { pi.SupplierId, pi.Supplier.Name })
                .Select(g => new
                {
                    SupplierId = g.Key.SupplierId,
                    SupplierName = g.Key.Name,
                    PurchaseCount = g.Count(),
                    TotalAmount = g.Sum(pi => pi.TotalAmount),
                    AverageAmount = g.Average(pi => pi.TotalAmount),
                    FirstPurchase = g.Min(pi => pi.InvoiceDate),
                    LastPurchase = g.Max(pi => pi.InvoiceDate)
                })
                .OrderByDescending(x => x.TotalAmount)
                .ToListAsync();
        }

        public async Task<decimal> GetSupplierTotalPurchasesAsync(int supplierId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _dbSet.Where(pi => pi.SupplierId == supplierId);

            if (fromDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= toDate.Value);

            return await query.SumAsync(pi => pi.TotalAmount);
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetLargePurchasesAsync(decimal minimumAmount)
        {
            return await _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                .Include(pi => pi.Supplier)
                .Where(pi => pi.TotalAmount >= minimumAmount)
                .OrderByDescending(pi => pi.TotalAmount)
                .ToListAsync();
        }

        public async Task<bool> IsInvoiceNumberUniqueAsync(string invoiceNumber, int? excludeId = null)
        {
            var query = _dbSet.Where(pi => pi.InvoiceNumber == invoiceNumber);
            
            if (excludeId.HasValue)
                query = query.Where(pi => pi.Id != excludeId.Value);

            return !await query.AnyAsync();
        }

        public async Task<string> GenerateNextInvoiceNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"PUR-{today:yyyyMMdd}-";
            
            var lastInvoice = await _dbSet
                .Where(pi => pi.InvoiceNumber.StartsWith(prefix))
                .OrderByDescending(pi => pi.InvoiceNumber)
                .FirstOrDefaultAsync();

            if (lastInvoice == null)
                return $"{prefix}001";

            var lastNumber = lastInvoice.InvoiceNumber.Substring(prefix.Length);
            if (int.TryParse(lastNumber, out int number))
                return $"{prefix}{(number + 1):D3}";

            return $"{prefix}001";
        }

        public async Task<bool> CanCancelInvoiceAsync(int invoiceId)
        {
            var invoice = await GetByIdAsync(invoiceId);
            if (invoice == null)
                return false;

            // Check if there are any returns for this invoice
            var hasReturns = await _context.PurchaseReturns.AnyAsync(pr => pr.PurchaseInvoiceId == invoiceId);
            
            // Check if invoice is older than allowed cancellation period (e.g., 7 days)
            var cancellationDeadline = DateTime.Now.AddDays(-7);
            var isWithinCancellationPeriod = invoice.InvoiceDate > cancellationDeadline;

            return !hasReturns && isWithinCancellationPeriod;
        }

        public async Task<IEnumerable<PurchaseReturn>> GetPurchaseReturnsAsync(int purchaseInvoiceId)
        {
            return await _context.PurchaseReturns
                .Include(pr => pr.Product)
                .Include(pr => pr.User)
                .Where(pr => pr.PurchaseInvoiceId == purchaseInvoiceId)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalReturnsAmountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.PurchaseReturns
                .Include(pr => pr.PurchaseInvoice)
                .Include(pr => pr.Product)
                .AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(pr => pr.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pr => pr.CreatedAt <= toDate.Value);

            // Calculate return amount based on original purchase price
            return await query
                .Join(_context.PurchaseInvoiceDetails,
                    pr => new { pr.PurchaseInvoiceId, pr.ProductId },
                    pid => new { PurchaseInvoiceId = pid.PurchaseInvoiceId, ProductId = pid.ProductId },
                    (pr, pid) => new { pr.Quantity, pid.UnitCost })
                .SumAsync(x => x.Quantity * x.UnitCost);
        }

        public async Task<IEnumerable<object>> GetReturnReasonsReportAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.PurchaseReturns.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(pr => pr.CreatedAt >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pr => pr.CreatedAt <= toDate.Value);

            return await query
                .GroupBy(pr => pr.Reason ?? "No Reason")
                .Select(g => new
                {
                    Reason = g.Key,
                    Count = g.Count(),
                    TotalQuantity = g.Sum(pr => pr.Quantity)
                })
                .OrderByDescending(x => x.Count)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetCostAnalysisAsync(int productId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.PurchaseInvoiceDetails
                .Include(pid => pid.PurchaseInvoice)
                .Where(pid => pid.ProductId == productId);

            if (fromDate.HasValue)
                query = query.Where(pid => pid.PurchaseInvoice.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pid => pid.PurchaseInvoice.InvoiceDate <= toDate.Value);

            return await query
                .Select(pid => new
                {
                    Date = pid.PurchaseInvoice.InvoiceDate,
                    Quantity = pid.Quantity,
                    UnitCost = pid.UnitCost,
                    TotalCost = pid.LineTotal,
                    SupplierName = pid.PurchaseInvoice.Supplier.Name
                })
                .OrderByDescending(x => x.Date)
                .ToListAsync();
        }

        public async Task<decimal> GetAverageCostAsync(int productId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.PurchaseInvoiceDetails
                .Include(pid => pid.PurchaseInvoice)
                .Where(pid => pid.ProductId == productId);

            if (fromDate.HasValue)
                query = query.Where(pid => pid.PurchaseInvoice.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pid => pid.PurchaseInvoice.InvoiceDate <= toDate.Value);

            if (!await query.AnyAsync())
                return 0;

            return await query.AverageAsync(pid => pid.UnitCost);
        }

        public async Task<IEnumerable<object>> GetPriceVariationReportAsync(int productId)
        {
            return await _context.PurchaseInvoiceDetails
                .Include(pid => pid.PurchaseInvoice)
                .Where(pid => pid.ProductId == productId)
                .GroupBy(pid => pid.UnitCost)
                .Select(g => new
                {
                    UnitCost = g.Key,
                    PurchaseCount = g.Count(),
                    TotalQuantity = g.Sum(pid => pid.Quantity),
                    FirstPurchase = g.Min(pid => pid.PurchaseInvoice.InvoiceDate),
                    LastPurchase = g.Max(pid => pid.PurchaseInvoice.InvoiceDate)
                })
                .OrderBy(x => x.UnitCost)
                .ToListAsync();
        }

        public async Task<PagedResult<PurchaseInvoice>> GetPagedPurchasesWithDetailsAsync(int pageNumber, int pageSize, 
            Expression<Func<PurchaseInvoice, bool>>? filter = null)
        {
            var query = _dbSet
                .Include(pi => pi.PurchaseInvoiceDetails)
                    .ThenInclude(pid => pid.Product)
                .Include(pi => pi.User)
                .Include(pi => pi.Supplier)
                .AsQueryable();

            if (filter != null)
                query = query.Where(filter);

            var totalCount = await query.CountAsync();
            var purchases = await query
                .OrderByDescending(pi => pi.InvoiceDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new PagedResult<PurchaseInvoice>
            {
                Data = purchases,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetRecentPurchasesAsync(int count = 10)
        {
            return await _dbSet
                .Include(pi => pi.Supplier)
                .Include(pi => pi.User)
                .OrderByDescending(pi => pi.InvoiceDate)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<object>> GetPurchaseTrendsAsync(int days = 30)
        {
            var fromDate = DateTime.Today.AddDays(-days);

            return await _dbSet
                .Where(pi => pi.InvoiceDate >= fromDate)
                .GroupBy(pi => pi.InvoiceDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    PurchaseCount = g.Count(),
                    TotalAmount = g.Sum(pi => pi.TotalAmount)
                })
                .OrderBy(x => x.Date)
                .ToListAsync();
        }

        public async Task<(IEnumerable<PurchaseInvoice> Purchases, int TotalCount)> GetPagedPurchasesAsync(
            int pageNumber, int pageSize, string? searchTerm = null, DateTime? fromDate = null, DateTime? toDate = null, 
            int? supplierId = null, int? userId = null)
        {
            var query = _dbSet
                .Include(pi => pi.User)
                .Include(pi => pi.Supplier)
                .AsQueryable();

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(pi => pi.InvoiceNumber.Contains(searchTerm) || 
                                         pi.Supplier.Name.Contains(searchTerm));
            }

            if (fromDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(pi => pi.InvoiceDate <= toDate.Value);

            if (supplierId.HasValue)
                query = query.Where(pi => pi.SupplierId == supplierId.Value);

            if (userId.HasValue)
                query = query.Where(pi => pi.UserId == userId.Value);

            var totalCount = await query.CountAsync();
            var purchases = await query
                .OrderByDescending(pi => pi.InvoiceDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (purchases, totalCount);
        }

        public async Task<bool> ValidatePurchaseDetailsAsync(IEnumerable<PurchaseInvoiceDetail> details)
        {
            foreach (var detail in details)
            {
                var product = await _context.Products.FindAsync(detail.ProductId);
                if (product == null)
                    return false;

                if (detail.Quantity <= 0 || detail.UnitCost < 0)
                    return false;
            }

            return true;
        }

        public async Task<bool> HasPendingReceiptsAsync(int supplierId)
        {
            // This would check for pending receipts or deliveries
            // For now, we'll just return false as we don't have these entities yet
            return false;
        }
    }
}

using System.Linq.Expressions;

namespace ElectronicsStore.Application.Interfaces
{
    public interface IGenericRepository<T> where T : class
    {
        // Basic CRUD Operations
        Task<T?> GetByIdAsync(int id);
        Task<T?> GetByIdAsync(object id);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> expression);
        Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> expression);
        Task<T?> LastOrDefaultAsync(Expression<Func<T, bool>> expression);
        Task<bool> AnyAsync(Expression<Func<T, bool>> expression);
        Task<int> CountAsync(Expression<Func<T, bool>>? expression = null);

        // Add Operations
        Task AddAsync(T entity);
        Task AddRangeAsync(IEnumerable<T> entities);

        // Update Operations
        void Update(T entity);
        void UpdateRange(IEnumerable<T> entities);
        Task<T> UpdateAsync(T entity);

        // Delete Operations
        void Remove(T entity);
        void RemoveRange(IEnumerable<T> entities);
        Task<bool> RemoveByIdAsync(int id);
        Task<bool> RemoveByIdAsync(object id);

        // Pagination with enhanced features
        Task<PagedResult<T>> GetPagedResultAsync(int pageNumber, int pageSize,
            Expression<Func<T, bool>>? filter = null,
            Func<IQueryable<T>, IOrderedQueryable<T>>? orderBy = null,
            params Expression<Func<T, object>>[] includes);

        Task<IEnumerable<T>> GetPagedAsync(int pageNumber, int pageSize,
            Expression<Func<T, bool>>? filter = null);

        // Include related entities
        Task<IEnumerable<T>> GetWithIncludeAsync(params Expression<Func<T, object>>[] includes);
        Task<T?> GetByIdWithIncludeAsync(int id, params Expression<Func<T, object>>[] includes);
        Task<IEnumerable<T>> FindWithIncludeAsync(Expression<Func<T, bool>> expression,
            params Expression<Func<T, object>>[] includes);

        // Ordering and Sorting
        Task<IEnumerable<T>> GetOrderedAsync<TKey>(Expression<Func<T, TKey>> orderBy, bool ascending = true);
        Task<IEnumerable<T>> GetOrderedAsync<TKey>(Expression<Func<T, bool>> filter,
            Expression<Func<T, TKey>> orderBy, bool ascending = true);

        // Advanced Queries
        Task<IEnumerable<TResult>> SelectAsync<TResult>(Expression<Func<T, TResult>> selector);
        Task<IEnumerable<TResult>> SelectAsync<TResult>(Expression<Func<T, bool>> filter,
            Expression<Func<T, TResult>> selector);

        // Aggregation
        Task<TResult> MaxAsync<TResult>(Expression<Func<T, TResult>> selector);
        Task<TResult> MinAsync<TResult>(Expression<Func<T, TResult>> selector);
        Task<decimal> SumAsync(Expression<Func<T, decimal>> selector);
        Task<double> AverageAsync(Expression<Func<T, int>> selector);
        Task<double> AverageAsync(Expression<Func<T, decimal>> selector);

        // Bulk Operations
        Task<int> BulkUpdateAsync(Expression<Func<T, bool>> filter, Expression<Func<T, T>> updateExpression);
        Task<int> BulkDeleteAsync(Expression<Func<T, bool>> filter);

        // Existence checks
        Task<bool> ExistsAsync(int id);
        Task<bool> ExistsAsync(object id);
        Task<bool> ExistsAsync(Expression<Func<T, bool>> expression);
    }

    // Helper class for pagination results
    public class PagedResult<T>
    {
        public IEnumerable<T> Data { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}

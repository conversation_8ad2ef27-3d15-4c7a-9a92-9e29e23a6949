namespace ElectronicsStore.Application.DTOs
{
    public class DashboardDto
    {
        public decimal TotalSales { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public decimal InventoryValue { get; set; }
        public List<DailySalesDto> RecentSales { get; set; } = new();
        public List<TopProductDto> TopProducts { get; set; } = new();
        public List<ExpenseCategoryDto> ExpenseBreakdown { get; set; } = new();
    }

    public class DailySalesDto
    {
        public DateTime Date { get; set; }
        public decimal Amount { get; set; }
        public int Count { get; set; }
    }

    public class FinancialReportDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCosts { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public List<MonthlyFinancialDto> MonthlyBreakdown { get; set; } = new();
    }

    public class MonthlyFinancialDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Costs { get; set; }
        public decimal Expenses { get; set; }
        public decimal Profit { get; set; }
    }

    public class InventoryReportDto
    {
        public int TotalProducts { get; set; }
        public decimal TotalValue { get; set; }
        public int LowStockCount { get; set; }
        public int OutOfStockCount { get; set; }
        public List<ProductStockDto> LowStockProducts { get; set; } = new();
        public List<ProductStockDto> OutOfStockProducts { get; set; } = new();
        public List<CategoryStockDto> CategoryBreakdown { get; set; } = new();
    }

    public class CategoryStockDto
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public int ProductCount { get; set; }
        public int TotalStock { get; set; }
        public decimal TotalValue { get; set; }
    }

    public class SalesAnalyticsDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalSales { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageSaleAmount { get; set; }
        public List<TopCustomerDto> TopCustomers { get; set; } = new();
        public List<TopProductDto> TopProducts { get; set; } = new();
        public List<SalesReportDto> DailySales { get; set; } = new();
        public Dictionary<string, decimal> PaymentMethodBreakdown { get; set; } = new();
    }

    public class PurchaseAnalyticsDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public int TotalPurchases { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AveragePurchaseAmount { get; set; }
        public List<TopSupplierDto> TopSuppliers { get; set; } = new();
        public List<PurchaseReportDto> DailyPurchases { get; set; } = new();
    }

    public class ProfitAnalysisDto
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCOGS { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal NetProfitMargin { get; set; }
        public List<ProductProfitDto> ProductProfitability { get; set; } = new();
    }

    public class ProductProfitDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public int QuantitySold { get; set; }
        public decimal Revenue { get; set; }
        public decimal COGS { get; set; }
        public decimal Profit { get; set; }
        public decimal ProfitMargin { get; set; }
    }

    public class ReportFilterDto
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? CategoryId { get; set; }
        public int? SupplierId { get; set; }
        public int? UserId { get; set; }
        public string? ReportType { get; set; }
        public string? GroupBy { get; set; } // daily, weekly, monthly, yearly
    }
}
